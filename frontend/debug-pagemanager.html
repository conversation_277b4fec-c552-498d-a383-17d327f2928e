<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>PageManager调试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1000px;
            margin: 20px auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .btn {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        .btn:hover {
            background: #0056b3;
        }
        .result {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 5px;
            padding: 15px;
            margin: 10px 0;
            white-space: pre-wrap;
            font-family: monospace;
            max-height: 400px;
            overflow-y: auto;
        }
        .error {
            background: #f8d7da;
            border-color: #f5c6cb;
            color: #721c24;
        }
        .success {
            background: #d4edda;
            border-color: #c3e6cb;
            color: #155724;
        }
        .warning {
            background: #fff3cd;
            border-color: #ffeaa7;
            color: #856404;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>PageManager调试工具</h1>
        <p>这个页面用于调试PageManager的初始化和功能问题。</p>
        
        <div>
            <button class="btn" onclick="checkPageManager()">检查PageManager状态</button>
            <button class="btn" onclick="testPageRegistration()">测试页面注册</button>
            <button class="btn" onclick="testPageShow()">测试页面显示</button>
            <button class="btn" onclick="clearLog()">清空日志</button>
        </div>
        
        <div id="result" class="result"></div>
    </div>

    <div class="container">
        <h2>页面容器</h2>
        <div id="mainContent" style="min-height: 200px; border: 1px solid #ddd; padding: 20px;">
            <!-- 页面内容将在这里显示 -->
        </div>
    </div>

    <!-- 按正确顺序引入JavaScript文件 -->
    <script src="js/config.js"></script>
    <script src="js/utils.js"></script>
    <script src="js/api.js"></script>
    <script src="js/auth.js"></script>
    <script src="js/components.js"></script>
    
    <!-- 核心框架 -->
    <script src="js/core/base-page.js"></script>
    <script src="js/core/page-manager.js"></script>
    
    <!-- 业务页面 -->
    <script src="js/pages/dashboard/dashboard.js"></script>
    <script src="js/pages/admin/user-management.js"></script>
    <script src="js/pages/admin/role-management.js"></script>
    <script src="js/pages/admin/permission-management.js"></script>
    <script src="js/pages/business/address-parse.js"></script>
    <script src="js/pages/business/file-management.js"></script>

    <script>
        function log(message, type = 'info') {
            const resultDiv = document.getElementById('result');
            const timestamp = new Date().toLocaleTimeString();
            const className = type === 'error' ? 'error' : (type === 'success' ? 'success' : (type === 'warning' ? 'warning' : ''));
            
            const logEntry = `[${timestamp}] ${message}\n`;
            resultDiv.textContent += logEntry;
            resultDiv.className = `result ${className}`;
            resultDiv.scrollTop = resultDiv.scrollHeight;
            
            // 同时输出到控制台
            console.log(`[PageManager Debug] ${message}`);
        }
        
        function clearLog() {
            document.getElementById('result').textContent = '';
            document.getElementById('result').className = 'result';
        }
        
        function checkPageManager() {
            log('=== 检查PageManager状态 ===');
            
            try {
                // 检查全局对象
                log(`window.PageManager存在: ${!!window.PageManager}`);
                
                if (window.PageManager) {
                    log(`PageManager类型: ${typeof window.PageManager}`);
                    log(`PageManager构造函数: ${window.PageManager.constructor.name}`);
                    
                    // 检查方法
                    log(`registerPage方法: ${typeof window.PageManager.registerPage}`);
                    log(`showPage方法: ${typeof window.PageManager.showPage}`);
                    log(`getCurrentPage方法: ${typeof window.PageManager.getCurrentPage}`);
                    
                    // 检查属性
                    log(`pages属性: ${typeof window.PageManager.pages}`);
                    if (window.PageManager.pages) {
                        log(`已注册页面数量: ${window.PageManager.pages.size}`);
                        
                        // 列出所有已注册的页面
                        const pageIds = Array.from(window.PageManager.pages.keys());
                        log(`已注册页面: ${pageIds.join(', ')}`);
                    }
                    
                    log('PageManager状态检查完成', 'success');
                } else {
                    log('PageManager未初始化！', 'error');
                }
                
            } catch (error) {
                log(`检查PageManager时出错: ${error.message}`, 'error');
            }
        }
        
        function testPageRegistration() {
            log('=== 测试页面注册 ===');
            
            try {
                if (!window.PageManager) {
                    log('PageManager不存在，无法测试注册', 'error');
                    return;
                }
                
                if (typeof window.PageManager.registerPage !== 'function') {
                    log('registerPage不是函数，无法测试注册', 'error');
                    return;
                }
                
                // 注册一个测试页面
                const testPageId = 'test-page-' + Date.now();
                
                window.PageManager.registerPage(testPageId, () => {
                    const page = document.createElement('div');
                    page.innerHTML = `<h3>测试页面 ${testPageId}</h3><p>这是一个测试页面，创建时间：${new Date().toLocaleString()}</p>`;
                    return page;
                });
                
                log(`成功注册测试页面: ${testPageId}`, 'success');
                log(`当前已注册页面数量: ${window.PageManager.pages.size}`);
                
            } catch (error) {
                log(`页面注册测试失败: ${error.message}`, 'error');
            }
        }
        
        function testPageShow() {
            log('=== 测试页面显示 ===');
            
            try {
                if (!window.PageManager) {
                    log('PageManager不存在，无法测试显示', 'error');
                    return;
                }
                
                if (typeof window.PageManager.showPage !== 'function') {
                    log('showPage不是函数，无法测试显示', 'error');
                    return;
                }
                
                // 获取已注册的页面列表
                const pageIds = Array.from(window.PageManager.pages.keys());
                
                if (pageIds.length === 0) {
                    log('没有已注册的页面可以测试', 'warning');
                    return;
                }
                
                // 测试显示第一个页面
                const testPageId = pageIds[0];
                log(`尝试显示页面: ${testPageId}`);
                
                window.PageManager.showPage(testPageId);
                
                log(`页面显示测试完成: ${testPageId}`, 'success');
                
                // 检查当前页面
                const currentPage = window.PageManager.getCurrentPage();
                log(`当前页面: ${currentPage}`);
                
            } catch (error) {
                log(`页面显示测试失败: ${error.message}`, 'error');
            }
        }
        
        // 页面加载完成后自动检查
        window.addEventListener('load', function() {
            log('页面加载完成，开始自动检查...');
            
            // 延迟一下确保所有脚本都加载完成
            setTimeout(() => {
                checkPageManager();
                
                // 如果有页面注册，测试显示
                if (window.PageManager && window.PageManager.pages && window.PageManager.pages.size > 0) {
                    log('发现已注册页面，测试显示功能...');
                    testPageShow();
                }
            }, 1000);
        });
        
        // 监听错误
        window.addEventListener('error', function(event) {
            log(`JavaScript错误: ${event.error.message}`, 'error');
        });
        
        // 监听未处理的Promise拒绝
        window.addEventListener('unhandledrejection', function(event) {
            log(`未处理的Promise拒绝: ${event.reason}`, 'error');
        });
    </script>
</body>
</html>
