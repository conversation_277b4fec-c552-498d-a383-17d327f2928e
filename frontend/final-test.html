<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>最终测试 - 清水河管理系统</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1000px;
            margin: 20px auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .btn {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        .btn:hover {
            background: #0056b3;
        }
        .btn-success {
            background: #28a745;
        }
        .btn-success:hover {
            background: #218838;
        }
        .result {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 5px;
            padding: 15px;
            margin: 10px 0;
            white-space: pre-wrap;
            font-family: monospace;
            max-height: 400px;
            overflow-y: auto;
        }
        .error {
            background: #f8d7da;
            border-color: #f5c6cb;
            color: #721c24;
        }
        .success {
            background: #d4edda;
            border-color: #c3e6cb;
            color: #155724;
        }
        .warning {
            background: #fff3cd;
            border-color: #ffeaa7;
            color: #856404;
        }
        .login-form {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 8px;
            margin: 20px 0;
        }
        .form-group {
            margin-bottom: 15px;
        }
        .form-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: 500;
        }
        .form-group input {
            width: 100%;
            padding: 8px 12px;
            border: 1px solid #ddd;
            border-radius: 4px;
            box-sizing: border-box;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>最终测试 - 清水河管理系统</h1>
        <p>测试所有修复是否生效</p>
        
        <div>
            <button class="btn" onclick="testAllModules()">测试所有模块</button>
            <button class="btn" onclick="testConnection()">测试连接</button>
            <button class="btn btn-success" onclick="testLogin()">测试登录</button>
            <button class="btn" onclick="clearLog()">清空日志</button>
        </div>
        
        <div class="login-form">
            <h3>登录测试</h3>
            <div class="form-group">
                <label for="username">用户名:</label>
                <input type="text" id="username" value="qingshuihe">
            </div>
            <div class="form-group">
                <label for="password">密码:</label>
                <input type="password" id="password" value="123456">
            </div>
            <button class="btn btn-success" onclick="performLogin()">执行登录</button>
        </div>
        
        <div id="result" class="result"></div>
    </div>

    <!-- 引入所有必要的JavaScript文件 -->
    <script src="js/config.js"></script>
    <script src="js/utils.js"></script>
    <script src="js/api.js"></script>
    <script src="js/auth.js"></script>
    <script src="js/components.js"></script>
    
    <!-- 核心框架 -->
    <script src="js/core/base-page.js"></script>
    <script src="js/core/page-manager.js"></script>
    <script src="js/page-manager-fallback.js"></script>

    <script>
        function log(message, type = 'info') {
            const resultDiv = document.getElementById('result');
            const timestamp = new Date().toLocaleTimeString();
            const className = type === 'error' ? 'error' : (type === 'success' ? 'success' : (type === 'warning' ? 'warning' : ''));
            
            const logEntry = `[${timestamp}] ${message}\n`;
            resultDiv.textContent += logEntry;
            resultDiv.className = `result ${className}`;
            resultDiv.scrollTop = resultDiv.scrollHeight;
            
            console.log(`[Final Test] ${message}`);
        }
        
        function clearLog() {
            document.getElementById('result').textContent = '';
            document.getElementById('result').className = 'result';
        }
        
        function testAllModules() {
            log('=== 测试所有模块 ===');
            
            // 测试CONFIG
            log(`CONFIG对象: ${typeof window.CONFIG}`);
            if (window.CONFIG) {
                log(`API_BASE_URL: ${CONFIG.API_BASE_URL}`);
            }
            
            // 测试API
            log(`API对象: ${typeof window.API}`);
            if (window.API) {
                log(`API.get方法: ${typeof API.get}`);
                log(`API.post方法: ${typeof API.post}`);
                log(`API.upload方法: ${typeof API.upload}`);
            }
            
            // 测试Utils
            log(`Utils对象: ${typeof window.Utils}`);
            if (window.Utils) {
                log(`Utils.formatDate方法: ${typeof Utils.formatDate}`);
            }
            
            // 测试Auth
            log(`Auth对象: ${typeof window.Auth}`);
            if (window.Auth) {
                log(`Auth.login方法: ${typeof Auth.login}`);
                log(`Auth.isAuthenticated方法: ${typeof Auth.isAuthenticated}`);
            }
            
            // 测试UI
            log(`UI对象: ${typeof window.UI}`);
            if (window.UI) {
                log(`UI.showMessage方法: ${typeof UI.showMessage}`);
                log(`UI.showConfirm方法: ${typeof UI.showConfirm}`);
            }
            
            // 测试PageManager
            log(`PageManager对象: ${typeof window.PageManager}`);
            if (window.PageManager) {
                log(`PageManager.registerPage方法: ${typeof PageManager.registerPage}`);
                log(`PageManager.showPage方法: ${typeof PageManager.showPage}`);
                log(`已注册页面数量: ${PageManager.pages?.size || 0}`);
            }
            
            log('模块测试完成', 'success');
        }
        
        async function testConnection() {
            log('=== 测试后端连接 ===');
            
            try {
                log(`正在连接: ${CONFIG.API_BASE_URL}`);
                
                const response = await fetch(`${CONFIG.API_BASE_URL}/admin/login`, {
                    method: 'OPTIONS',
                    headers: {
                        'Content-Type': 'application/json',
                        'Accept': 'application/json'
                    }
                });
                
                log(`连接状态: ${response.status}`, response.ok ? 'success' : 'warning');
                
                if (response.ok) {
                    log('后端连接正常', 'success');
                } else {
                    log(`连接异常: ${response.status}`, 'error');
                }
                
            } catch (error) {
                log(`连接失败: ${error.message}`, 'error');
            }
        }
        
        async function testLogin() {
            log('=== 测试登录API ===');
            
            try {
                const loginData = {
                    username: 'qingshuihe',
                    password: '123456'
                };
                
                log('正在调用API.post方法...');
                const result = await API.post(CONFIG.API_ENDPOINTS.AUTH.LOGIN, loginData);
                
                log(`登录结果: ${JSON.stringify(result, null, 2)}`);
                
                if (result && result.code === 200) {
                    log('API登录测试成功！', 'success');
                    if (result.data && result.data.token) {
                        log(`获得Token: ${result.data.token.substring(0, 50)}...`);
                    }
                } else {
                    log(`登录失败: ${result?.message || '未知错误'}`, 'error');
                }
                
            } catch (error) {
                log(`登录测试失败: ${error.message}`, 'error');
            }
        }
        
        async function performLogin() {
            log('=== 执行完整登录流程 ===');
            
            try {
                const username = document.getElementById('username').value;
                const password = document.getElementById('password').value;
                
                if (!username || !password) {
                    log('请输入用户名和密码', 'warning');
                    return;
                }
                
                log(`正在登录用户: ${username}`);
                
                const result = await Auth.login(username, password);
                
                if (result) {
                    log('登录成功！', 'success');
                    log(`认证状态: ${Auth.isAuthenticated()}`);
                    
                    const currentUser = Auth.getCurrentUser();
                    if (currentUser) {
                        log(`当前用户: ${JSON.stringify(currentUser, null, 2)}`);
                    }
                    
                    log('登录流程完成，可以进入主应用', 'success');
                } else {
                    log('登录失败', 'error');
                }
                
            } catch (error) {
                log(`登录流程失败: ${error.message}`, 'error');
            }
        }
        
        // 页面加载时自动测试
        window.onload = function() {
            log('页面加载完成，开始自动测试...');
            
            setTimeout(() => {
                testAllModules();
                testConnection();
            }, 500);
        };
    </script>
</body>
</html>
