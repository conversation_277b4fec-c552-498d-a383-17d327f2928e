/**
 * 主应用文件
 * 这个文件是应用的入口点，负责初始化和协调各个模块
 */

// 主应用类
class Application {
    constructor() {
        this.isInitialized = false;
        this.currentPage = null;
    }
    
    /**
     * 初始化应用
     */
    async init() {
        if (this.isInitialized) {
            return;
        }
        
        try {
            Utils.log.info('正在初始化应用...');
            
            // 等待DOM加载完成
            if (document.readyState === 'loading') {
                await new Promise(resolve => {
                    document.addEventListener('DOMContentLoaded', resolve);
                });
            }
            
            // 检查PageManager状态
            Utils.log.debug('检查PageManager状态:', {
                exists: !!window.PageManager,
                registerPage: typeof window.PageManager?.registerPage,
                showPage: typeof window.PageManager?.showPage
            });

            // 初始化各个模块
            this.initializeModules();

            // 绑定全局事件
            this.bindGlobalEvents();

            // 等待一下确保所有页面都注册完成
            await new Promise(resolve => setTimeout(resolve, 1000));

            // 检查登录状态并显示相应页面
            await this.initializeAuth();
            
            this.isInitialized = true;
            Utils.log.info('应用初始化完成');
            
        } catch (error) {
            Utils.log.error('应用初始化失败:', error);
            UI.showMessage('应用初始化失败，请刷新页面重试', 'error');
        }
    }
    
    /**
     * 初始化各个模块
     */
    initializeModules() {
        // UI模块已在components.js中初始化
        // Auth模块已在auth.js中初始化
        // Pages模块已在pages.js中初始化
        
        Utils.log.debug('所有模块初始化完成');
    }
    
    /**
     * 绑定全局事件
     */
    bindGlobalEvents() {
        // 登录表单事件
        const loginForm = document.getElementById('loginForm');
        if (loginForm) {
            loginForm.addEventListener('submit', this.handleLogin.bind(this));
        }
        
        // 登出按钮事件
        const logoutBtn = document.getElementById('logoutBtn');
        if (logoutBtn) {
            logoutBtn.addEventListener('click', this.handleLogout.bind(this));
        }
        
        // 侧边栏导航事件
        const sidebarLinks = document.querySelectorAll('.sidebar a[data-page]');
        sidebarLinks.forEach(link => {
            link.addEventListener('click', (e) => {
                e.preventDefault();
                const pageId = link.dataset.page;

                try {
                    // 直接调用PageManager
                    window.PageManager.showPage(pageId);
                } catch (error) {
                    console.error('PageManager调用失败，使用降级处理:', error);
                    // 降级处理：使用应用的showPage方法
                    this.showPage(pageId);
                }
            });
        });

        // 仪表板卡片按钮事件
        const cardButtons = document.querySelectorAll('.card-btn[data-page]');
        cardButtons.forEach(button => {
            button.addEventListener('click', (e) => {
                const pageId = button.dataset.page;

                try {
                    // 直接调用PageManager
                    window.PageManager.showPage(pageId);
                } catch (error) {
                    console.error('PageManager调用失败，使用降级处理:', error);
                    // 降级处理：使用应用的showPage方法
                    this.showPage(pageId);
                }
            });
        });
        
        // 全局键盘事件
        document.addEventListener('keydown', this.handleGlobalKeydown.bind(this));
        
        // 窗口大小变化事件
        window.addEventListener('resize', Utils.debounce(() => {
            this.handleWindowResize();
        }, 250));
        
        // 页面可见性变化事件
        document.addEventListener('visibilitychange', () => {
            if (!document.hidden && Auth.isAuthenticated()) {
                // 页面重新可见时，验证token有效性
                Auth.validateToken();
            }
        });
        
        Utils.log.debug('全局事件绑定完成');
    }
    
    /**
     * 初始化认证状态
     */
    async initializeAuth() {
        // 再次检查PageManager状态
        Utils.log.debug('initializeAuth - PageManager状态:', {
            exists: !!window.PageManager,
            showPage: typeof window.PageManager?.showPage,
            pages: window.PageManager?.pages?.size || 0
        });

        if (Auth.isAuthenticated()) {
            // 已登录，显示仪表板
            this.showPage(CONFIG.ROUTES.DASHBOARD);
            Utils.log.info('用户已登录，显示仪表板');
        } else {
            // 未登录，显示登录页
            this.showPage(CONFIG.ROUTES.LOGIN);
            Utils.log.info('用户未登录，显示登录页');
        }
    }
    
    /**
     * 处理登录
     */
    async handleLogin(event) {
        event.preventDefault();
        
        const formData = new FormData(event.target);
        const credentials = {
            username: formData.get('username'),
            password: formData.get('password')
        };
        
        // 基本验证
        if (!credentials.username || !credentials.password) {
            UI.showMessage('请输入用户名和密码', 'warning');
            return;
        }
        
        // 执行登录
        const success = await Auth.login(credentials);
        
        if (success) {
            // 登录成功，跳转到仪表板
            this.showPage(CONFIG.ROUTES.DASHBOARD);
        }
    }
    
    /**
     * 处理登出
     */
    async handleLogout() {
        const confirmed = await UI.confirm({
            title: '确认退出',
            message: '确定要退出登录吗？',
            confirmText: '退出',
            cancelText: '取消'
        });
        
        if (confirmed) {
            await Auth.logout();
        }
    }
    
    /**
     * 显示页面
     */
    showPage(pageId) {
        Utils.log.debug('显示页面:', pageId);

        // 特殊页面处理
        if (pageId === CONFIG.ROUTES.LOGIN) {
            this.showLoginPage();
        } else if (pageId === CONFIG.ROUTES.DASHBOARD || pageId === 'dashboardPage') {
            // 尝试使用新的PageManager显示仪表板
            try {
                window.PageManager.showPage('dashboard');
            } catch (error) {
                Utils.log.error('PageManager调用失败，使用降级处理:', error);
                this.showDashboardPage();
            }
        } else {
            // 其他功能页面通过PageManager处理
            try {
                window.PageManager.showPage(pageId);
            } catch (error) {
                Utils.log.error('PageManager调用失败:', error);
                UI.showMessage('页面加载失败，请稍后重试', 'error');
            }
        }

        this.currentPage = pageId;
    }
    
    /**
     * 显示登录页面
     */
    showLoginPage() {
        const allPages = document.querySelectorAll('.page');
        allPages.forEach(page => {
            page.classList.remove('active');
        });
        
        const loginPage = document.getElementById('loginPage');
        if (loginPage) {
            loginPage.classList.add('active');
        }
        
        // 隐藏侧边栏
        const sidebar = document.getElementById('sidebar');
        if (sidebar) {
            sidebar.style.display = 'none';
        }
    }
    
    /**
     * 显示仪表板页面
     */
    showDashboardPage() {
        const allPages = document.querySelectorAll('.page');
        allPages.forEach(page => {
            page.classList.remove('active');
        });
        
        const dashboardPage = document.getElementById('dashboardPage');
        if (dashboardPage) {
            dashboardPage.classList.add('active');
        }
        
        // 显示侧边栏
        const sidebar = document.getElementById('sidebar');
        if (sidebar) {
            sidebar.style.display = 'block';
        }
        
        // 更新侧边栏激活状态
        const sidebarLinks = document.querySelectorAll('.sidebar a');
        sidebarLinks.forEach(link => {
            link.classList.remove('active');
        });
    }
    
    /**
     * 处理全局键盘事件
     */
    handleGlobalKeydown(event) {
        // Esc键关闭模态框
        if (event.key === 'Escape') {
            const modal = document.querySelector('.modal-overlay.show');
            if (modal) {
                const closeBtn = modal.querySelector('.modal-close');
                if (closeBtn) {
                    closeBtn.click();
                }
            }
        }
        
        // Ctrl+/ 显示快捷键帮助（可选功能）
        if (event.ctrlKey && event.key === '/') {
            event.preventDefault();
            this.showShortcutHelp();
        }
    }
    
    /**
     * 处理窗口大小变化
     */
    handleWindowResize() {
        // 响应式处理逻辑
        const sidebar = document.getElementById('sidebar');
        const isMobile = window.innerWidth <= 768;
        
        if (sidebar && Auth.isAuthenticated()) {
            if (isMobile) {
                // 移动端可以添加特殊处理
                sidebar.style.display = 'block';
            } else {
                sidebar.style.display = 'block';
            }
        }
    }
    
    /**
     * 显示快捷键帮助
     */
    showShortcutHelp() {
        const helpContent = `
            <div style="padding: 20px;">
                <h4>快捷键说明</h4>
                <ul style="list-style: none; padding: 0; margin-top: 20px;">
                    <li style="margin-bottom: 10px;"><kbd>Esc</kbd> - 关闭模态框</li>
                    <li style="margin-bottom: 10px;"><kbd>Ctrl + /</kbd> - 显示快捷键帮助</li>
                </ul>
            </div>
        `;
        
        UI.createModal({
            title: '快捷键帮助',
            content: helpContent,
            size: 'small'
        });
    }
    
    /**
     * 获取当前页面
     */
    getCurrentPage() {
        return this.currentPage;
    }
    
    /**
     * 应用是否已初始化
     */
    isReady() {
        return this.isInitialized;
    }
}

// 创建应用实例
const App = new Application();

// 全局应用对象
window.App = App;

// 自动初始化应用
App.init().catch(error => {
    console.error('应用启动失败:', error);
});

// 导出应用实例（如果在模块环境中）
if (typeof module !== 'undefined' && module.exports) {
    module.exports = App;
}
