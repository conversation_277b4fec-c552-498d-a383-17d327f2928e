/**
 * 工具函数文件
 * 这个文件包含了应用中常用的工具函数，如日期格式化、数据验证等
 */

// 工具函数对象
const Utils = {
    
    /**
     * 日期格式化
     * @param {Date|string|number} date - 日期对象、日期字符串或时间戳
     * @param {string} format - 格式字符串，如 'YYYY-MM-DD HH:mm:ss'
     * @returns {string} 格式化后的日期字符串
     */
    formatDate(date, format = 'YYYY-MM-DD HH:mm:ss') {
        if (!date) return '';
        
        const d = new Date(date);
        if (isNaN(d.getTime())) return '';
        
        const year = d.getFullYear();
        const month = String(d.getMonth() + 1).padStart(2, '0');
        const day = String(d.getDate()).padStart(2, '0');
        const hours = String(d.getHours()).padStart(2, '0');
        const minutes = String(d.getMinutes()).padStart(2, '0');
        const seconds = String(d.getSeconds()).padStart(2, '0');
        
        return format
            .replace('YYYY', year)
            .replace('MM', month)
            .replace('DD', day)
            .replace('HH', hours)
            .replace('mm', minutes)
            .replace('ss', seconds);
    },
    
    /**
     * 文件大小格式化
     * @param {number} bytes - 字节数
     * @returns {string} 格式化后的文件大小
     */
    formatFileSize(bytes) {
        if (bytes === 0) return '0 B';
        
        const k = 1024;
        const sizes = ['B', 'KB', 'MB', 'GB', 'TB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    },
    
    /**
     * 防抖函数
     * @param {Function} func - 要防抖的函数
     * @param {number} wait - 等待时间（毫秒）
     * @returns {Function} 防抖后的函数
     */
    debounce(func, wait) {
        let timeout;
        return function executedFunction(...args) {
            const later = () => {
                clearTimeout(timeout);
                func(...args);
            };
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
        };
    },
    
    /**
     * 节流函数
     * @param {Function} func - 要节流的函数
     * @param {number} limit - 限制时间（毫秒）
     * @returns {Function} 节流后的函数
     */
    throttle(func, limit) {
        let inThrottle;
        return function(...args) {
            if (!inThrottle) {
                func.apply(this, args);
                inThrottle = true;
                setTimeout(() => inThrottle = false, limit);
            }
        };
    },
    
    /**
     * 深拷贝对象
     * @param {any} obj - 要拷贝的对象
     * @returns {any} 拷贝后的对象
     */
    deepClone(obj) {
        if (obj === null || typeof obj !== 'object') return obj;
        if (obj instanceof Date) return new Date(obj.getTime());
        if (obj instanceof Array) return obj.map(item => this.deepClone(item));
        if (typeof obj === 'object') {
            const clonedObj = {};
            for (const key in obj) {
                if (obj.hasOwnProperty(key)) {
                    clonedObj[key] = this.deepClone(obj[key]);
                }
            }
            return clonedObj;
        }
    },
    
    /**
     * 生成唯一ID
     * @returns {string} 唯一ID
     */
    generateId() {
        return Date.now().toString(36) + Math.random().toString(36).substr(2);
    },
    
    /**
     * 验证邮箱格式
     * @param {string} email - 邮箱地址
     * @returns {boolean} 是否有效
     */
    validateEmail(email) {
        return CONFIG.VALIDATION.EMAIL.PATTERN.test(email);
    },
    
    /**
     * 验证手机号格式
     * @param {string} phone - 手机号
     * @returns {boolean} 是否有效
     */
    validatePhone(phone) {
        return CONFIG.VALIDATION.PHONE.PATTERN.test(phone);
    },
    
    /**
     * 验证用户名格式
     * @param {string} username - 用户名
     * @returns {boolean} 是否有效
     */
    validateUsername(username) {
        const { MIN_LENGTH, MAX_LENGTH, PATTERN } = CONFIG.VALIDATION.USERNAME;
        return username.length >= MIN_LENGTH && 
               username.length <= MAX_LENGTH && 
               PATTERN.test(username);
    },
    
    /**
     * 验证密码强度
     * @param {string} password - 密码
     * @returns {object} 验证结果
     */
    validatePassword(password) {
        const { MIN_LENGTH, MAX_LENGTH, PATTERN } = CONFIG.VALIDATION.PASSWORD;
        
        const result = {
            isValid: false,
            strength: 'weak',
            messages: []
        };
        
        if (password.length < MIN_LENGTH) {
            result.messages.push(`密码长度至少${MIN_LENGTH}位`);
        }
        
        if (password.length > MAX_LENGTH) {
            result.messages.push(`密码长度不能超过${MAX_LENGTH}位`);
        }
        
        if (!/[a-z]/.test(password)) {
            result.messages.push('密码必须包含小写字母');
        }
        
        if (!/[A-Z]/.test(password)) {
            result.messages.push('密码必须包含大写字母');
        }
        
        if (!/\d/.test(password)) {
            result.messages.push('密码必须包含数字');
        }
        
        // 计算密码强度
        let strength = 0;
        if (password.length >= MIN_LENGTH) strength++;
        if (/[a-z]/.test(password)) strength++;
        if (/[A-Z]/.test(password)) strength++;
        if (/\d/.test(password)) strength++;
        if (/[^a-zA-Z\d]/.test(password)) strength++;
        
        if (strength >= 4) result.strength = 'strong';
        else if (strength >= 3) result.strength = 'medium';
        
        result.isValid = result.messages.length === 0;
        
        return result;
    },
    
    /**
     * 获取文件扩展名
     * @param {string} filename - 文件名
     * @returns {string} 扩展名
     */
    getFileExtension(filename) {
        return filename.slice((filename.lastIndexOf('.') - 1 >>> 0) + 2);
    },
    
    /**
     * 检查文件类型是否允许
     * @param {string} fileType - 文件MIME类型
     * @returns {boolean} 是否允许
     */
    isFileTypeAllowed(fileType) {
        return CONFIG.CONSTANTS.FILE_UPLOAD.ALLOWED_TYPES.includes(fileType);
    },
    
    /**
     * 检查文件大小是否超限
     * @param {number} fileSize - 文件大小（字节）
     * @returns {boolean} 是否超限
     */
    isFileSizeExceeded(fileSize) {
        return fileSize > CONFIG.CONSTANTS.FILE_UPLOAD.MAX_SIZE;
    },
    
    /**
     * 转义HTML字符
     * @param {string} text - 要转义的文本
     * @returns {string} 转义后的文本
     */
    escapeHtml(text) {
        const map = {
            '&': '&amp;',
            '<': '&lt;',
            '>': '&gt;',
            '"': '&quot;',
            "'": '&#039;'
        };
        return text.replace(/[&<>"']/g, m => map[m]);
    },
    
    /**
     * 获取URL参数
     * @param {string} name - 参数名
     * @returns {string|null} 参数值
     */
    getUrlParameter(name) {
        const urlParams = new URLSearchParams(window.location.search);
        return urlParams.get(name);
    },
    
    /**
     * 设置URL参数
     * @param {string} name - 参数名
     * @param {string} value - 参数值
     */
    setUrlParameter(name, value) {
        const url = new URL(window.location);
        url.searchParams.set(name, value);
        window.history.pushState({}, '', url);
    },
    
    /**
     * 本地存储操作
     */
    storage: {
        /**
         * 设置本地存储
         * @param {string} key - 键名
         * @param {any} value - 值
         */
        set(key, value) {
            try {
                localStorage.setItem(key, JSON.stringify(value));
            } catch (e) {
                console.error('设置本地存储失败:', e);
            }
        },
        
        /**
         * 获取本地存储
         * @param {string} key - 键名
         * @param {any} defaultValue - 默认值
         * @returns {any} 存储的值
         */
        get(key, defaultValue = null) {
            try {
                const item = localStorage.getItem(key);
                return item ? JSON.parse(item) : defaultValue;
            } catch (e) {
                console.error('获取本地存储失败:', e);
                return defaultValue;
            }
        },
        
        /**
         * 删除本地存储
         * @param {string} key - 键名
         */
        remove(key) {
            try {
                localStorage.removeItem(key);
            } catch (e) {
                console.error('删除本地存储失败:', e);
            }
        },
        
        /**
         * 清空本地存储
         */
        clear() {
            try {
                localStorage.clear();
            } catch (e) {
                console.error('清空本地存储失败:', e);
            }
        }
    },
    
    /**
     * 日志输出（根据环境配置）
     */
    log: {
        debug(...args) {
            if (CONFIG.DEBUG && CONFIG.LOG_LEVEL === 'debug') {
                console.log('[DEBUG]', ...args);
            }
        },
        
        info(...args) {
            if (CONFIG.DEBUG && ['debug', 'info'].includes(CONFIG.LOG_LEVEL)) {
                console.info('[INFO]', ...args);
            }
        },
        
        warn(...args) {
            if (CONFIG.DEBUG && ['debug', 'info', 'warn'].includes(CONFIG.LOG_LEVEL)) {
                console.warn('[WARN]', ...args);
            }
        },
        
        error(...args) {
            console.error('[ERROR]', ...args);
        }
    }
};

// 全局工具对象
window.Utils = Utils;
