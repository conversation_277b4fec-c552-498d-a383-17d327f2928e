/**
 * API调用文件
 * 这个文件包含了所有与后端API交互的函数
 */

// API调用类
class ApiClient {
    constructor() {
        this.baseURL = CONFIG.API_BASE_URL;
        this.timeout = CONFIG.CONSTANTS.REQUEST.TIMEOUT;
        this.retryCount = CONFIG.CONSTANTS.REQUEST.RETRY_COUNT;
    }
    
    /**
     * 获取请求头
     * @returns {Object} 请求头对象
     */
    getHeaders() {
        const headers = {
            'Content-Type': 'application/json',
            'Accept': 'application/json'
        };

        // 添加认证token - 根据后端要求使用 'token' 头而不是 'Authorization'
        const tokenKey = CONFIG.CONSTANTS.STORAGE_KEYS.TOKEN;
        const token = localStorage.getItem(tokenKey); // 直接获取字符串，不进行JSON解析
        if (token) {
            headers['token'] = token;
            Utils.log.debug('添加token到请求头:', token.substring(0, 20) + '...');
        } else {
            Utils.log.debug('未找到token，发送未认证请求');
        }

        return headers;
    }
    
    /**
     * 发送HTTP请求
     * @param {string} method - 请求方法
     * @param {string} url - 请求URL
     * @param {Object} data - 请求数据
     * @param {Object} options - 请求选项
     * @returns {Promise} 请求Promise
     */
    async request(method, url, data = null, options = {}) {
        const fullUrl = url.startsWith('http') ? url : `${this.baseURL}${url}`;
        
        const config = {
            method: method.toUpperCase(),
            headers: this.getHeaders(),
            ...options
        };
        
        // 添加请求体
        if (data && ['POST', 'PUT', 'PATCH'].includes(config.method)) {
            if (data instanceof FormData) {
                // 如果是FormData，删除Content-Type让浏览器自动设置
                delete config.headers['Content-Type'];
                config.body = data;
            } else {
                config.body = JSON.stringify(data);
            }
        }
        
        // 添加查询参数
        let requestUrl = fullUrl;
        if (data && config.method === 'GET') {
            const params = new URLSearchParams(data);
            const separator = requestUrl.includes('?') ? '&' : '?';
            requestUrl += separator + params.toString();
        }
        
        Utils.log.debug('API请求:', config.method, requestUrl, data);
        Utils.log.debug('请求头:', config.headers);

        try {
            // 设置超时
            const controller = new AbortController();
            const timeoutId = setTimeout(() => controller.abort(), this.timeout);
            config.signal = controller.signal;

            const response = await fetch(requestUrl, config);
            clearTimeout(timeoutId);
            
            Utils.log.debug('API响应:', response.status, response.statusText);
            
            // 检查响应状态
            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }
            
            // 解析响应数据
            const contentType = response.headers.get('content-type');
            let responseData;
            
            if (contentType && contentType.includes('application/json')) {
                responseData = await response.json();
            } else {
                responseData = await response.text();
            }
            
            Utils.log.debug('API响应数据:', responseData);
            
            return responseData;
            
        } catch (error) {
            Utils.log.error('API请求失败:', error);
            
            // 处理特定错误
            if (error.name === 'AbortError') {
                throw new Error('请求超时，请稍后重试');
            }
            
            if (error.message.includes('401')) {
                // 未授权，清除token并跳转到登录页
                Auth.logout();
                throw new Error('登录已过期，请重新登录');
            }
            
            if (error.message.includes('403')) {
                throw new Error('没有权限访问此资源');
            }
            
            if (error.message.includes('404')) {
                throw new Error('请求的资源不存在');
            }
            
            if (error.message.includes('500')) {
                throw new Error('服务器内部错误，请稍后重试');
            }
            
            throw error;
        }
    }
    
    /**
     * GET请求
     */
    get(url, params = null, options = {}) {
        return this.request('GET', url, params, options);
    }
    
    /**
     * POST请求
     */
    post(url, data = null, options = {}) {
        return this.request('POST', url, data, options);
    }
    
    /**
     * PUT请求
     */
    put(url, data = null, options = {}) {
        return this.request('PUT', url, data, options);
    }
    
    /**
     * DELETE请求
     */
    delete(url, data = null, options = {}) {
        return this.request('DELETE', url, data, options);
    }
    
    /**
     * 文件上传
     */
    upload(url, files, onProgress = null) {
        const formData = new FormData();
        
        // 添加文件
        if (Array.isArray(files)) {
            files.forEach((file, index) => {
                formData.append('files', file);
            });
        } else {
            formData.append('files', files);
        }
        
        const options = {};
        
        // 添加上传进度监听
        if (onProgress && typeof onProgress === 'function') {
            // 注意：fetch API不支持上传进度，这里使用XMLHttpRequest
            return new Promise((resolve, reject) => {
                const xhr = new XMLHttpRequest();
                
                xhr.upload.addEventListener('progress', (e) => {
                    if (e.lengthComputable) {
                        const percentComplete = (e.loaded / e.total) * 100;
                        onProgress(percentComplete);
                    }
                });
                
                xhr.addEventListener('load', () => {
                    if (xhr.status >= 200 && xhr.status < 300) {
                        try {
                            const response = JSON.parse(xhr.responseText);
                            resolve(response);
                        } catch (e) {
                            resolve(xhr.responseText);
                        }
                    } else {
                        reject(new Error(`HTTP ${xhr.status}: ${xhr.statusText}`));
                    }
                });
                
                xhr.addEventListener('error', () => {
                    reject(new Error('上传失败'));
                });
                
                xhr.open('POST', `${this.baseURL}${url}`);

                // 设置请求头（不包括Content-Type，让浏览器自动设置）
                const headers = this.getHeaders();
                delete headers['Content-Type'];

                Object.keys(headers).forEach(key => {
                    xhr.setRequestHeader(key, headers[key]);
                });
                
                xhr.send(formData);
            });
        }
        
        return this.request('POST', url, formData, options);
    }
}

// 创建API客户端实例
const api = new ApiClient();

// API接口封装
const API = {
    // 直接暴露基础方法
    get: (url, params, options) => api.get(url, params, options),
    post: (url, data, options) => api.post(url, data, options),
    put: (url, data, options) => api.put(url, data, options),
    delete: (url, options) => api.delete(url, options),
    upload: (url, formData, options) => api.upload(url, formData, options),

    // 认证相关
    auth: {
        /**
         * 用户登录
         * @param {Object} credentials - 登录凭据
         * @returns {Promise} 登录结果
         */
        login(credentials) {
            return api.post(CONFIG.API_ENDPOINTS.AUTH.LOGIN, credentials);
        },
        
        /**
         * 用户登出
         * @returns {Promise} 登出结果
         */
        logout() {
            return api.post(CONFIG.API_ENDPOINTS.AUTH.LOGOUT);
        }
    },
    
    // 用户管理
    user: {
        /**
         * 修改用户信息
         * @param {Object} userData - 用户数据
         * @returns {Promise} 修改结果
         */
        modify(userData) {
            return api.post(CONFIG.API_ENDPOINTS.USER.MODIFY, userData);
        },
        
        /**
         * 查询用户列表
         * @param {Object} queryParams - 查询参数
         * @returns {Promise} 用户列表
         */
        query(queryParams) {
            return api.post(CONFIG.API_ENDPOINTS.USER.QUERY, queryParams);
        },
        
        /**
         * 重置密码
         * @param {string} password - 新密码
         * @returns {Promise} 重置结果
         */
        resetPassword(password) {
            return api.post(CONFIG.API_ENDPOINTS.USER.RESET_PASSWORD, { pwd: password });
        },
        
        /**
         * 发送重置密码链接
         * @param {string} email - 邮箱地址
         * @returns {Promise} 发送结果
         */
        sendResetLink(email) {
            return api.post(CONFIG.API_ENDPOINTS.USER.SEND_RESET_LINK, { email });
        },
        
        /**
         * 发送验证码
         * @param {string} email - 邮箱地址
         * @returns {Promise} 发送结果
         */
        sendVerifyCode(email) {
            return api.post(CONFIG.API_ENDPOINTS.USER.SEND_VERIFY_CODE, { email });
        },
        
        /**
         * 根据ID查询用户
         * @param {number} id - 用户ID
         * @returns {Promise} 用户信息
         */
        getById(id) {
            return api.post(`${CONFIG.API_ENDPOINTS.USER.QUERY_BY_ID}/${id}`);
        },
        
        /**
         * 根据ID删除用户
         * @param {number} id - 用户ID
         * @returns {Promise} 删除结果
         */
        deleteById(id) {
            return api.delete(`${CONFIG.API_ENDPOINTS.USER.DELETE_BY_ID}/${id}`);
        },
        
        /**
         * 批量删除用户
         * @param {Array} ids - 用户ID数组
         * @returns {Promise} 删除结果
         */
        deleteByIds(ids) {
            return api.delete(CONFIG.API_ENDPOINTS.USER.DELETE_BY_IDS, { ids });
        }
    },
    
    // 角色管理
    role: {
        /**
         * 修改角色信息
         * @param {Object} roleData - 角色数据
         * @returns {Promise} 修改结果
         */
        modify(roleData) {
            return api.post(CONFIG.API_ENDPOINTS.ROLE.MODIFY, roleData);
        },
        
        /**
         * 查询角色列表
         * @param {Object} queryParams - 查询参数
         * @returns {Promise} 角色列表
         */
        query(queryParams) {
            return api.post(CONFIG.API_ENDPOINTS.ROLE.QUERY, queryParams);
        },
        
        /**
         * 修改角色用户关系
         * @param {Object} relationData - 关系数据
         * @returns {Promise} 修改结果
         */
        modifyUserRelation(relationData) {
            return api.post(CONFIG.API_ENDPOINTS.ROLE.MODIFY_USER_RELATION, relationData);
        }
    },
    
    // 权限管理
    permission: {
        /**
         * 修改权限信息
         * @param {Object} permissionData - 权限数据
         * @returns {Promise} 修改结果
         */
        modify(permissionData) {
            return api.post(CONFIG.API_ENDPOINTS.PERMISSION.MODIFY, permissionData);
        },
        
        /**
         * 查询权限列表
         * @param {Object} queryParams - 查询参数
         * @returns {Promise} 权限列表
         */
        query(queryParams) {
            return api.post(CONFIG.API_ENDPOINTS.PERMISSION.QUERY, queryParams);
        },
        
        /**
         * 修改权限角色关系
         * @param {Object} relationData - 关系数据
         * @returns {Promise} 修改结果
         */
        modifyRoleRelation(relationData) {
            return api.post(CONFIG.API_ENDPOINTS.PERMISSION.MODIFY_ROLE_RELATION, relationData);
        }
    },
    
    // 文件管理
    file: {
        /**
         * 上传文件
         * @param {FileList|File[]} files - 文件列表
         * @param {Function} onProgress - 进度回调
         * @returns {Promise} 上传结果
         */
        upload(files, onProgress) {
            return api.upload(CONFIG.API_ENDPOINTS.FILE.UPLOAD, files, onProgress);
        },
        
        /**
         * 下载文件
         * @param {Array} fileIds - 文件ID列表
         * @returns {Promise} 下载结果
         */
        download(fileIds) {
            return api.post(CONFIG.API_ENDPOINTS.FILE.DOWNLOAD, fileIds);
        }
    },
    
    // 地址解析
    address: {
        /**
         * 解析地址
         * @param {Array} persons - 人员地址列表
         * @returns {Promise} 解析结果
         */
        parse(persons) {
            return api.post(CONFIG.API_ENDPOINTS.ADDRESS.PARSE, persons);
        },
        
        /**
         * 获取测试数据
         * @returns {Promise} 测试数据
         */
        getTestData() {
            return api.get(CONFIG.API_ENDPOINTS.ADDRESS.TEST_DATA);
        }
    }
};

// 全局API对象
window.API = API;
