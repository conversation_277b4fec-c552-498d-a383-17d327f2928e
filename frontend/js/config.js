/**
 * 配置文件
 * 这个文件包含了应用的所有配置信息，如API地址、常量等
 */

// API 基础配置
const CONFIG = {
    // 后端API基础地址 - 根据你的Spring Boot应用端口配置
    API_BASE_URL: 'http://localhost:8080',
    
    // API 端点配置
    API_ENDPOINTS: {
        // 用户认证相关
        AUTH: {
            LOGIN: '/admin/login',
            LOGOUT: '/admin/logout'
        },
        
        // 用户管理相关
        USER: {
            MODIFY: '/admin/modifyUser',
            QUERY: '/admin/queryUser',
            RESET_PASSWORD: '/admin/resetpw',
            SEND_RESET_LINK: '/admin/sendResetLink',
            SEND_VERIFY_CODE: '/admin/sendVerifyCode',
            QUERY_BY_ID: '/admin/queryUserById',
            DELETE_BY_ID: '/admin/deleteUserById',
            DELETE_BY_IDS: '/admin/deleteUserByIds'
        },
        
        // 角色管理相关
        ROLE: {
            MODIFY: '/admin/modifyRole',
            QUERY: '/admin/queryRole',
            DELETE_BY_ID: '/admin/deleteRoleById',
            MODIFY_USER_RELATION: '/admin/modifyRoleUserRelation'
        },
        
        // 权限管理相关
        PERMISSION: {
            MODIFY: '/admin/modifyPermission',
            QUERY: '/admin/queryPermission',
            DELETE_BY_ID: '/admin/deletePermissionById',
            MODIFY_ROLE_RELATION: '/admin/modifyRolePermissionRelation'
        },
        
        // 文件上传下载相关
        FILE: {
            UPLOAD: '/common/uploadFiles',
            DOWNLOAD: '/common/downloadFiles'
        },
        
        // 地址解析相关
        ADDRESS: {
            PARSE: '/api/address/parse',
            TEST_DATA: '/api/address/test-data'
        }
    },
    
    // 应用常量
    CONSTANTS: {
        // 本地存储键名
        STORAGE_KEYS: {
            TOKEN: 'qsh_token',
            USER_INFO: 'qsh_user_info',
            REMEMBER_LOGIN: 'qsh_remember_login'
        },
        
        // 分页配置
        PAGINATION: {
            DEFAULT_PAGE_SIZE: 10,
            PAGE_SIZE_OPTIONS: [5, 10, 20, 50, 100]
        },
        
        // 文件上传配置
        FILE_UPLOAD: {
            MAX_SIZE: 1024 * 1024 * 1024, // 1GB
            ALLOWED_TYPES: [
                'image/jpeg', 'image/png', 'image/gif', 'image/webp',
                'application/pdf', 'application/msword', 
                'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
                'application/vnd.ms-excel',
                'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
                'text/plain', 'text/csv'
            ]
        },
        
        // 消息提示配置
        MESSAGE: {
            DURATION: 3000, // 3秒
            TYPES: {
                SUCCESS: 'success',
                ERROR: 'error',
                WARNING: 'warning',
                INFO: 'info'
            }
        },
        
        // 请求超时配置
        REQUEST: {
            TIMEOUT: 30000, // 30秒
            RETRY_COUNT: 3
        }
    },
    
    // 页面路由配置
    ROUTES: {
        LOGIN: 'loginPage',
        DASHBOARD: 'dashboardPage',
        ADDRESS_PARSE: 'address-parse',
        ADDRESS_TEST: 'address-test',
        USER_MANAGEMENT: 'user-management',
        ROLE_MANAGEMENT: 'role-management',
        PERMISSION_MANAGEMENT: 'permission-management',
        FILE_UPLOAD: 'file-upload',
        FILE_DOWNLOAD: 'file-download'
    },
    
    // 表单验证规则
    VALIDATION: {
        USERNAME: {
            MIN_LENGTH: 3,
            MAX_LENGTH: 20,
            PATTERN: /^[a-zA-Z0-9_]+$/
        },
        PASSWORD: {
            MIN_LENGTH: 6,
            MAX_LENGTH: 20,
            PATTERN: /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)[a-zA-Z\d@$!%*?&]/
        },
        EMAIL: {
            PATTERN: /^[^\s@]+@[^\s@]+\.[^\s@]+$/
        },
        PHONE: {
            PATTERN: /^1[3-9]\d{9}$/
        }
    },
    
    // 主题配置
    THEME: {
        PRIMARY_COLOR: '#667eea',
        SECONDARY_COLOR: '#764ba2',
        SUCCESS_COLOR: '#28a745',
        DANGER_COLOR: '#dc3545',
        WARNING_COLOR: '#ffc107',
        INFO_COLOR: '#17a2b8'
    }
};

// 环境配置
const ENV_CONFIG = {
    // 开发环境
    development: {
        API_BASE_URL: 'http://localhost:8080',
        DEBUG: true,
        LOG_LEVEL: 'debug'
    },
    
    // 测试环境
    test: {
        API_BASE_URL: 'http://test-server:8080',
        DEBUG: true,
        LOG_LEVEL: 'info'
    },
    
    // 生产环境
    production: {
        API_BASE_URL: 'https://api.qingshuihe.com',
        DEBUG: false,
        LOG_LEVEL: 'error'
    }
};

// 获取当前环境配置
function getCurrentEnvConfig() {
    const hostname = window.location.hostname;
    
    if (hostname === 'localhost' || hostname === '127.0.0.1') {
        return ENV_CONFIG.development;
    } else if (hostname.includes('test')) {
        return ENV_CONFIG.test;
    } else {
        return ENV_CONFIG.production;
    }
}

// 合并环境配置到主配置
const currentEnv = getCurrentEnvConfig();
Object.assign(CONFIG, currentEnv);

// 导出配置（如果在模块环境中）
if (typeof module !== 'undefined' && module.exports) {
    module.exports = CONFIG;
}

// 全局配置对象
window.CONFIG = CONFIG;
