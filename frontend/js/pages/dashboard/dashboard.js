/**
 * 仪表板页面
 */
class DashboardPage extends BasePage {
    constructor() {
        super('dashboard', '仪表板');
        this.stats = {};
    }
    
    /**
     * 创建页面元素
     */
    createElement() {
        const page = document.createElement('div');
        page.innerHTML = `
            <div class="dashboard-container">
                <div class="dashboard-header">
                    <h3 class="dashboard-title">
                        <i class="fas fa-tachometer-alt"></i>
                        系统仪表板
                    </h3>
                    <p class="dashboard-description">
                        欢迎使用清水河管理系统，这里是您的工作概览
                    </p>
                </div>
                
                <!-- 统计卡片 -->
                <div class="stats-grid">
                    <div class="stat-card">
                        <div class="stat-icon">
                            <i class="fas fa-users"></i>
                        </div>
                        <div class="stat-content">
                            <div class="stat-number" id="userCount">-</div>
                            <div class="stat-label">用户总数</div>
                        </div>
                    </div>
                    
                    <div class="stat-card">
                        <div class="stat-icon">
                            <i class="fas fa-user-tag"></i>
                        </div>
                        <div class="stat-content">
                            <div class="stat-number" id="roleCount">-</div>
                            <div class="stat-label">角色数量</div>
                        </div>
                    </div>
                    
                    <div class="stat-card">
                        <div class="stat-icon">
                            <i class="fas fa-key"></i>
                        </div>
                        <div class="stat-content">
                            <div class="stat-number" id="permissionCount">-</div>
                            <div class="stat-label">权限数量</div>
                        </div>
                    </div>
                    
                    <div class="stat-card">
                        <div class="stat-icon">
                            <i class="fas fa-map-marker-alt"></i>
                        </div>
                        <div class="stat-content">
                            <div class="stat-number" id="addressCount">-</div>
                            <div class="stat-label">地址解析</div>
                        </div>
                    </div>
                </div>
                
                <!-- 快速操作 -->
                <div class="quick-actions">
                    <h4>
                        <i class="fas fa-bolt"></i>
                        快速操作
                    </h4>
                    <div class="action-grid">
                        <div class="action-card" data-page="user-management">
                            <div class="action-icon">
                                <i class="fas fa-users"></i>
                            </div>
                            <div class="action-content">
                                <div class="action-title">用户管理</div>
                                <div class="action-description">管理系统用户</div>
                            </div>
                        </div>
                        
                        <div class="action-card" data-page="role-management">
                            <div class="action-icon">
                                <i class="fas fa-user-tag"></i>
                            </div>
                            <div class="action-content">
                                <div class="action-title">角色管理</div>
                                <div class="action-description">管理用户角色</div>
                            </div>
                        </div>
                        
                        <div class="action-card" data-page="permission-management">
                            <div class="action-icon">
                                <i class="fas fa-key"></i>
                            </div>
                            <div class="action-content">
                                <div class="action-title">权限管理</div>
                                <div class="action-description">管理系统权限</div>
                            </div>
                        </div>
                        
                        <div class="action-card" data-page="address-parse">
                            <div class="action-icon">
                                <i class="fas fa-map-marker-alt"></i>
                            </div>
                            <div class="action-content">
                                <div class="action-title">地址解析</div>
                                <div class="action-description">智能地址解析</div>
                            </div>
                        </div>
                        
                        <div class="action-card" data-page="file-management">
                            <div class="action-icon">
                                <i class="fas fa-folder-open"></i>
                            </div>
                            <div class="action-content">
                                <div class="action-title">文件管理</div>
                                <div class="action-description">上传下载文件</div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- 系统信息 -->
                <div class="system-info">
                    <h4>
                        <i class="fas fa-info-circle"></i>
                        系统信息
                    </h4>
                    <div class="info-grid">
                        <div class="info-item">
                            <div class="info-label">当前用户:</div>
                            <div class="info-value" id="currentUser">-</div>
                        </div>
                        <div class="info-item">
                            <div class="info-label">登录时间:</div>
                            <div class="info-value" id="loginTime">-</div>
                        </div>
                        <div class="info-item">
                            <div class="info-label">系统版本:</div>
                            <div class="info-value">v1.0.0</div>
                        </div>
                        <div class="info-item">
                            <div class="info-label">最后更新:</div>
                            <div class="info-value">${Utils.formatDate(new Date())}</div>
                        </div>
                    </div>
                </div>
            </div>
        `;
        return page;
    }
    
    /**
     * 绑定事件
     */
    bindEvents() {
        // 快速操作卡片点击事件
        const actionCards = this.element.querySelectorAll('.action-card');
        actionCards.forEach(card => {
            card.addEventListener('click', () => {
                const pageId = card.dataset.page;
                if (pageId) {
                    PageManager.showPage(pageId);
                }
            });
        });
    }
    
    /**
     * 初始化数据
     */
    async initData() {
        // 加载统计数据
        await this.loadStats();
        
        // 更新用户信息
        this.updateUserInfo();
    }
    
    /**
     * 加载统计数据
     */
    async loadStats() {
        try {
            // 并行加载各种统计数据
            const [userStats, roleStats, permissionStats] = await Promise.allSettled([
                this.loadUserStats(),
                this.loadRoleStats(),
                this.loadPermissionStats()
            ]);
            
            // 更新统计显示
            this.updateStatsDisplay();
            
        } catch (error) {
            Utils.log.error('加载统计数据错误:', error);
        }
    }
    
    /**
     * 加载用户统计
     */
    async loadUserStats() {
        try {
            const result = await API.post(CONFIG.API_ENDPOINTS.USER.QUERY, {
                current: 1,
                size: 1,
                paramObj: {}
            });
            
            if (result && result.code === 200) {
                this.stats.userCount = result.total || 0;
            }
        } catch (error) {
            Utils.log.error('加载用户统计错误:', error);
            this.stats.userCount = 0;
        }
    }
    
    /**
     * 加载角色统计
     */
    async loadRoleStats() {
        try {
            const result = await API.post(CONFIG.API_ENDPOINTS.ROLE.QUERY, {
                current: 1,
                size: 1,
                paramObj: {}
            });
            
            if (result && result.code === 200) {
                this.stats.roleCount = result.total || 0;
            }
        } catch (error) {
            Utils.log.error('加载角色统计错误:', error);
            this.stats.roleCount = 0;
        }
    }
    
    /**
     * 加载权限统计
     */
    async loadPermissionStats() {
        try {
            const result = await API.post(CONFIG.API_ENDPOINTS.PERMISSION.QUERY, {
                current: 1,
                size: 1,
                paramObj: {}
            });
            
            if (result && result.code === 200) {
                this.stats.permissionCount = result.total || 0;
            }
        } catch (error) {
            Utils.log.error('加载权限统计错误:', error);
            this.stats.permissionCount = 0;
        }
    }
    
    /**
     * 更新统计显示
     */
    updateStatsDisplay() {
        // 更新用户数量
        const userCountElement = this.element.querySelector('#userCount');
        if (userCountElement) {
            userCountElement.textContent = this.stats.userCount || 0;
        }
        
        // 更新角色数量
        const roleCountElement = this.element.querySelector('#roleCount');
        if (roleCountElement) {
            roleCountElement.textContent = this.stats.roleCount || 0;
        }
        
        // 更新权限数量
        const permissionCountElement = this.element.querySelector('#permissionCount');
        if (permissionCountElement) {
            permissionCountElement.textContent = this.stats.permissionCount || 0;
        }
        
        // 地址解析数量（模拟数据）
        const addressCountElement = this.element.querySelector('#addressCount');
        if (addressCountElement) {
            addressCountElement.textContent = '116';
        }
    }
    
    /**
     * 更新用户信息
     */
    updateUserInfo() {
        const currentUser = window.Auth ? Auth.getCurrentUser() : null;

        // 更新当前用户
        const currentUserElement = this.element.querySelector('#currentUser');
        if (currentUserElement) {
            if (currentUser && currentUser.username) {
                currentUserElement.textContent = currentUser.username;
            } else {
                currentUserElement.textContent = '-';
            }
        }

        // 更新登录时间（从localStorage获取）
        const loginTimeElement = this.element.querySelector('#loginTime');
        if (loginTimeElement) {
            const loginTime = localStorage.getItem('qsh_login_time');
            if (loginTime) {
                loginTimeElement.textContent = Utils.formatDate(new Date(loginTime));
            } else {
                loginTimeElement.textContent = Utils.formatDate(new Date());
                localStorage.setItem('qsh_login_time', new Date().toISOString());
            }
        }
    }
}

// 延迟注册，确保PageManager已初始化
setTimeout(() => {
    try {
        console.log('注册Dashboard页面...');

        // 注册页面
        window.PageManager.registerPage('dashboard', async () => {
            console.log('创建Dashboard页面实例...');
            const page = new DashboardPage();
            return await page.show();
        });

        // 注册仪表板页面（兼容旧的页面ID）
        window.PageManager.registerPage('dashboardPage', async () => {
            console.log('创建DashboardPage页面实例...');
            const page = new DashboardPage();
            return await page.show();
        });

        console.log('Dashboard页面注册成功');
    } catch (error) {
        console.error('Dashboard页面注册失败:', error);
    }
}, 200);
