/**
 * 角色管理页面
 */
class RoleManagementPage extends BaseTablePage {
    constructor() {
        super('role-management', '角色管理', {
            searchable: true,
            addable: true,
            editable: true,
            deletable: true
        });
    }
    
    /**
     * 创建页面元素
     */
    createElement() {
        const page = document.createElement('div');
        page.innerHTML = `
            <div class="table-container">
                <div class="table-header">
                    <h3 class="table-title">
                        <i class="fas fa-user-tag"></i>
                        角色管理
                    </h3>
                    <div class="table-actions">
                        <button class="btn btn-primary" id="addRoleBtn">
                            <i class="fas fa-plus"></i>
                            添加角色
                        </button>
                        <button class="btn btn-secondary" id="refreshRoleBtn">
                            <i class="fas fa-sync"></i>
                            刷新
                        </button>
                    </div>
                </div>
                
                <!-- 搜索区域 -->
                <div class="search-container">
                    <div class="search-form">
                        <div class="form-group">
                            <label>角色名称:</label>
                            <input type="text" id="roleNameSearch" placeholder="请输入角色名称">
                        </div>
                        <div class="form-group">
                            <label>角色代码:</label>
                            <input type="text" id="roleCodeSearch" placeholder="请输入角色代码">
                        </div>
                        <div class="form-actions">
                            <button class="btn btn-primary" id="searchRoleBtn">
                                <i class="fas fa-search"></i>
                                搜索
                            </button>
                            <button class="btn btn-secondary" id="resetRoleSearchBtn">
                                <i class="fas fa-undo"></i>
                                重置
                            </button>
                        </div>
                    </div>
                </div>
                
                <!-- 角色列表 -->
                <div id="tableContainer">
                    <div class="loading">
                        <i class="fas fa-spinner fa-spin"></i>
                        正在加载角色数据...
                    </div>
                </div>
            </div>
        `;
        return page;
    }
    
    /**
     * 绑定事件
     */
    bindEvents() {
        // 添加角色
        this.element.querySelector('#addRoleBtn').addEventListener('click', () => {
            this.handleAdd();
        });
        
        // 刷新
        this.element.querySelector('#refreshRoleBtn').addEventListener('click', () => {
            this.refresh();
        });
        
        // 搜索
        this.element.querySelector('#searchRoleBtn').addEventListener('click', () => {
            this.performSearch();
        });
        
        // 重置搜索
        this.element.querySelector('#resetRoleSearchBtn').addEventListener('click', () => {
            this.resetSearch();
        });
    }
    
    /**
     * 获取表格列配置
     */
    getColumns() {
        return [
            { key: 'id', title: 'ID', width: '80px' },
            { key: 'name', title: '角色名称' },
            { key: 'code', title: '角色代码' },
            { key: 'createDate', title: '创建时间', type: 'date' },
            { key: 'createBy', title: '创建者' }
        ];
    }
    
    /**
     * 获取表格操作按钮配置
     */
    getActions() {
        return [
            {
                text: '编辑',
                className: 'btn-primary',
                icon: 'fas fa-edit',
                handler: (role) => this.handleEdit(role)
            },
            {
                text: '分配用户',
                className: 'btn-info',
                icon: 'fas fa-users',
                handler: (role) => this.handleAssignUsers(role)
            },
            {
                text: '删除',
                className: 'btn-danger',
                icon: 'fas fa-trash',
                handler: (role) => this.handleDelete(role)
            }
        ];
    }
    
    /**
     * 加载数据
     */
    async loadData(searchParams = {}) {
        const queryData = {
            current: 1,
            size: 10,
            paramObj: searchParams
        };
        
        const result = await API.post(CONFIG.API_ENDPOINTS.ROLE.QUERY, queryData);
        
        if (result && result.records) {
            return result.records || [];
        } else {
            throw new Error(result?.message || '加载角色数据失败');
        }
    }
    
    /**
     * 初始化数据
     */
    async initData() {
        const data = await this.loadData();
        this.displayData(data);
    }
    
    /**
     * 执行搜索
     */
    performSearch() {
        const name = this.element.querySelector('#roleNameSearch').value.trim();
        const code = this.element.querySelector('#roleCodeSearch').value.trim();
        
        const searchParams = {};
        if (name) searchParams.name = name;
        if (code) searchParams.code = code;
        
        this.handleSearch(searchParams);
    }
    
    /**
     * 重置搜索
     */
    resetSearch() {
        this.element.querySelector('#roleNameSearch').value = '';
        this.element.querySelector('#roleCodeSearch').value = '';
        this.handleSearch({});
    }
    
    /**
     * 处理添加角色
     */
    handleAdd() {
        this.showRoleModal();
    }
    
    /**
     * 处理编辑角色
     */
    handleEdit(role) {
        this.showRoleModal(role);
    }
    
    /**
     * 处理删除角色
     */
    async handleDelete(role) {
        const confirmed = await UI.showConfirm(
            '确认删除',
            `确定要删除角色"${role.name}"吗？此操作不可恢复。`
        );
        
        if (!confirmed) return;
        
        try {
            const result = await API.post(CONFIG.API_ENDPOINTS.ROLE.DELETE_BY_ID, {
                id: role.id
            });
            
            if (result && result.code === 200) {
                UI.showMessage('角色删除成功', 'success');
                this.refresh();
            } else {
                UI.showMessage(result?.message || '删除失败', 'error');
            }
        } catch (error) {
            Utils.log.error('删除角色错误:', error);
            UI.showMessage('网络错误，请稍后重试', 'error');
        }
    }
    
    /**
     * 处理分配用户
     */
    handleAssignUsers(role) {
        // 这里可以打开用户分配模态框
        // 为了简化，暂时只显示消息
        UI.showMessage(`为角色"${role.name}"分配用户功能开发中`, 'info');
    }
    
    /**
     * 显示角色编辑模态框
     */
    showRoleModal(role = null) {
        const isEdit = !!role;
        const title = isEdit ? '编辑角色' : '添加角色';
        
        const modalContent = `
            <form id="roleForm">
                <div class="form-group">
                    <label for="roleName">角色名称 <span class="required">*</span></label>
                    <input type="text" id="roleName" name="name" required 
                           value="${role?.name || ''}" placeholder="请输入角色名称">
                </div>
                <div class="form-group">
                    <label for="roleCode">角色代码 <span class="required">*</span></label>
                    <input type="text" id="roleCode" name="code" required 
                           value="${role?.code || ''}" placeholder="请输入角色代码">
                </div>
                ${isEdit ? `<input type="hidden" name="id" value="${role.id}">` : ''}
            </form>
        `;
        
        UI.showModal(title, modalContent, [
            {
                text: '取消',
                className: 'btn-secondary',
                handler: () => UI.hideModal()
            },
            {
                text: isEdit ? '更新' : '创建',
                className: 'btn-primary',
                handler: () => this.saveRole(isEdit)
            }
        ]);
    }
    
    /**
     * 保存角色
     */
    async saveRole(isEdit) {
        try {
            const form = document.getElementById('roleForm');
            const formData = new FormData(form);
            const roleData = Object.fromEntries(formData.entries());
            
            // 验证数据
            if (!roleData.name || !roleData.code) {
                UI.showMessage('请填写完整的角色信息', 'error');
                return;
            }
            
            const result = await API.post(CONFIG.API_ENDPOINTS.ROLE.MODIFY, roleData);
            
            if (result && result.code === 200) {
                UI.hideModal();
                UI.showMessage(isEdit ? '角色更新成功' : '角色创建成功', 'success');
                this.refresh();
            } else {
                UI.showMessage(result?.message || '操作失败', 'error');
            }
        } catch (error) {
            Utils.log.error('保存角色错误:', error);
            UI.showMessage('网络错误，请稍后重试', 'error');
        }
    }
}

// 延迟注册页面，确保PageManager已初始化
setTimeout(() => {
    try {
        window.PageManager.registerPage('role-management', async () => {
            const page = new RoleManagementPage();
            return await page.show();
        });
        console.log('RoleManagement页面注册成功');
    } catch (error) {
        console.error('RoleManagement页面注册失败:', error);
    }
}, 200);
