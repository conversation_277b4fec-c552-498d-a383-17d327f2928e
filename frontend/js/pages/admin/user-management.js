/**
 * 用户管理页面
 */
class UserManagementPage extends BaseTablePage {
    constructor() {
        super('user-management', '用户管理', {
            searchable: true,
            addable: true,
            editable: true,
            deletable: true
        });
    }
    
    /**
     * 创建页面元素
     */
    createElement() {
        const page = document.createElement('div');
        page.innerHTML = `
            <div class="table-container">
                <div class="table-header">
                    <h3 class="table-title">
                        <i class="fas fa-users"></i>
                        用户管理
                    </h3>
                    <div class="table-actions">
                        <button class="btn btn-primary" id="addUserBtn">
                            <i class="fas fa-plus"></i>
                            添加用户
                        </button>
                        <button class="btn btn-secondary" id="refreshUserBtn">
                            <i class="fas fa-sync"></i>
                            刷新
                        </button>
                    </div>
                </div>
                
                <!-- 搜索区域 -->
                <div class="search-container">
                    <div class="search-form">
                        <div class="form-group">
                            <label>用户名:</label>
                            <input type="text" id="usernameSearch" placeholder="请输入用户名">
                        </div>
                        <div class="form-group">
                            <label>邮箱:</label>
                            <input type="text" id="emailSearch" placeholder="请输入邮箱">
                        </div>
                        <div class="form-actions">
                            <button class="btn btn-primary" id="searchUserBtn">
                                <i class="fas fa-search"></i>
                                搜索
                            </button>
                            <button class="btn btn-secondary" id="resetUserSearchBtn">
                                <i class="fas fa-undo"></i>
                                重置
                            </button>
                        </div>
                    </div>
                </div>
                
                <!-- 用户列表 -->
                <div id="tableContainer">
                    <div class="loading">
                        <i class="fas fa-spinner fa-spin"></i>
                        正在加载用户数据...
                    </div>
                </div>
            </div>
        `;
        return page;
    }
    
    /**
     * 绑定事件
     */
    bindEvents() {
        // 添加用户
        this.element.querySelector('#addUserBtn').addEventListener('click', () => {
            this.handleAdd();
        });
        
        // 刷新
        this.element.querySelector('#refreshUserBtn').addEventListener('click', () => {
            this.refresh();
        });
        
        // 搜索
        this.element.querySelector('#searchUserBtn').addEventListener('click', () => {
            this.performSearch();
        });
        
        // 重置搜索
        this.element.querySelector('#resetUserSearchBtn').addEventListener('click', () => {
            this.resetSearch();
        });
    }
    
    /**
     * 获取表格列配置
     */
    getColumns() {
        return [
            { key: 'id', title: 'ID', width: '80px' },
            { key: 'username', title: '用户名' },
            { key: 'email', title: '邮箱' },
            { 
                key: 'enable', 
                title: '状态',
                formatter: (value) => {
                    return value === 0 ? 
                        '<span class="badge badge-success">启用</span>' : 
                        '<span class="badge badge-danger">禁用</span>';
                }
            },
            { key: 'createDate', title: '创建时间', type: 'date' },
            { key: 'createBy', title: '创建者' }
        ];
    }
    
    /**
     * 获取表格操作按钮配置
     */
    getActions() {
        return [
            {
                text: '编辑',
                className: 'btn-primary',
                icon: 'fas fa-edit',
                handler: (user) => this.handleEdit(user)
            },
            {
                text: '分配角色',
                className: 'btn-info',
                icon: 'fas fa-user-tag',
                handler: (user) => this.handleAssignRoles(user)
            },
            {
                text: '删除',
                className: 'btn-danger',
                icon: 'fas fa-trash',
                handler: (user) => this.handleDelete(user)
            }
        ];
    }
    
    /**
     * 加载数据
     */
    async loadData(searchParams = {}) {
        const queryData = {
            current: 1,
            size: 10,
            paramObj: searchParams
        };
        
        const result = await API.post(CONFIG.API_ENDPOINTS.USER.QUERY, queryData);
        
        if (result && result.records) {
            return result.records || [];
        } else {
            throw new Error(result?.message || '加载用户数据失败');
        }
    }
    
    /**
     * 初始化数据
     */
    async initData() {
        const data = await this.loadData();
        this.displayData(data);
    }
    
    /**
     * 执行搜索
     */
    performSearch() {
        const username = this.element.querySelector('#usernameSearch').value.trim();
        const email = this.element.querySelector('#emailSearch').value.trim();
        
        const searchParams = {};
        if (username) searchParams.username = username;
        if (email) searchParams.email = email;
        
        this.handleSearch(searchParams);
    }
    
    /**
     * 重置搜索
     */
    resetSearch() {
        this.element.querySelector('#usernameSearch').value = '';
        this.element.querySelector('#emailSearch').value = '';
        this.handleSearch({});
    }
    
    /**
     * 处理添加用户
     */
    handleAdd() {
        this.showUserModal();
    }
    
    /**
     * 处理编辑用户
     */
    handleEdit(user) {
        this.showUserModal(user);
    }
    
    /**
     * 处理删除用户
     */
    async handleDelete(user) {
        const confirmed = await UI.showConfirm(
            '确认删除',
            `确定要删除用户"${user.username}"吗？此操作不可恢复。`
        );

        if (!confirmed) return;

        try {
            const result = await API.post(CONFIG.API_ENDPOINTS.USER.DELETE_BY_ID, {
                id: user.id
            });

            if (result && result.code === 200) {
                UI.showMessage('用户删除成功', 'success');
                this.refresh();
            } else {
                UI.showMessage(result?.message || '删除失败', 'error');
            }
        } catch (error) {
            Utils.log.error('删除用户错误:', error);
            UI.showMessage('网络错误，请稍后重试', 'error');
        }
    }
    
    /**
     * 处理分配角色
     */
    handleAssignRoles(user) {
        // 这里可以打开角色分配模态框
        // 为了简化，暂时只显示消息
        UI.showMessage(`为用户"${user.username}"分配角色功能开发中`, 'info');
    }
    
    /**
     * 显示用户编辑模态框
     */
    showUserModal(user = null) {
        const isEdit = !!user;
        const title = isEdit ? '编辑用户' : '添加用户';
        
        const modalContent = `
            <form id="userForm">
                <div class="form-group">
                    <label for="username">用户名 <span class="required">*</span></label>
                    <input type="text" id="username" name="username" required 
                           value="${user?.username || ''}" placeholder="请输入用户名"
                           ${isEdit ? 'readonly' : ''}>
                </div>
                <div class="form-group">
                    <label for="email">邮箱 <span class="required">*</span></label>
                    <input type="email" id="email" name="email" required 
                           value="${user?.email || ''}" placeholder="请输入邮箱">
                </div>
                ${!isEdit ? `
                <div class="form-group">
                    <label for="password">密码 <span class="required">*</span></label>
                    <input type="password" id="password" name="password" required 
                           placeholder="请输入密码">
                </div>
                ` : ''}
                <div class="form-group">
                    <label for="enable">状态:</label>
                    <select id="enable" name="enable">
                        <option value="0" ${user?.enable === 0 ? 'selected' : ''}>启用</option>
                        <option value="1" ${user?.enable === 1 ? 'selected' : ''}>禁用</option>
                    </select>
                </div>
                ${isEdit ? `<input type="hidden" name="id" value="${user.id}">` : ''}
            </form>
        `;
        
        UI.showModal(title, modalContent, [
            {
                text: '取消',
                className: 'btn-secondary',
                handler: () => UI.hideModal()
            },
            {
                text: isEdit ? '更新' : '创建',
                className: 'btn-primary',
                handler: () => this.saveUser(isEdit)
            }
        ]);
    }
    
    /**
     * 保存用户
     */
    async saveUser(isEdit) {
        try {
            const form = document.getElementById('userForm');
            const formData = new FormData(form);
            const userData = Object.fromEntries(formData.entries());
            
            // 验证数据
            if (!userData.username || !userData.email) {
                UI.showMessage('请填写完整的用户信息', 'error');
                return;
            }
            
            if (!isEdit && !userData.password) {
                UI.showMessage('请输入密码', 'error');
                return;
            }
            
            const result = await API.post(CONFIG.API_ENDPOINTS.USER.MODIFY, userData);
            
            if (result && result.code === 200) {
                UI.hideModal();
                UI.showMessage(isEdit ? '用户更新成功' : '用户创建成功', 'success');
                this.refresh();
            } else {
                UI.showMessage(result?.message || '操作失败', 'error');
            }
        } catch (error) {
            Utils.log.error('保存用户错误:', error);
            UI.showMessage('网络错误，请稍后重试', 'error');
        }
    }
}

// 延迟注册页面，确保PageManager已初始化
setTimeout(() => {
    try {
        window.PageManager.registerPage('user-management', async () => {
            const page = new UserManagementPage();
            return await page.show();
        });
        console.log('UserManagement页面注册成功');
    } catch (error) {
        console.error('UserManagement页面注册失败:', error);
    }
}, 200);
