/**
 * 权限管理页面
 */
class PermissionManagementPage extends BaseTablePage {
    constructor() {
        super('permission-management', '权限管理', {
            searchable: true,
            addable: true,
            editable: true,
            deletable: true
        });
    }
    
    /**
     * 创建页面元素
     */
    createElement() {
        const page = document.createElement('div');
        page.innerHTML = `
            <div class="table-container">
                <div class="table-header">
                    <h3 class="table-title">
                        <i class="fas fa-key"></i>
                        权限管理
                    </h3>
                    <div class="table-actions">
                        <button class="btn btn-primary" id="addPermissionBtn">
                            <i class="fas fa-plus"></i>
                            添加权限
                        </button>
                        <button class="btn btn-secondary" id="refreshPermissionBtn">
                            <i class="fas fa-sync"></i>
                            刷新
                        </button>
                    </div>
                </div>
                
                <!-- 搜索区域 -->
                <div class="search-container">
                    <div class="search-form">
                        <div class="form-group">
                            <label>权限链接:</label>
                            <input type="text" id="permissionUrlSearch" placeholder="请输入权限链接">
                        </div>
                        <div class="form-group">
                            <label>权限描述:</label>
                            <input type="text" id="permissionDescSearch" placeholder="请输入权限描述">
                        </div>
                        <div class="form-actions">
                            <button class="btn btn-primary" id="searchPermissionBtn">
                                <i class="fas fa-search"></i>
                                搜索
                            </button>
                            <button class="btn btn-secondary" id="resetPermissionSearchBtn">
                                <i class="fas fa-undo"></i>
                                重置
                            </button>
                        </div>
                    </div>
                </div>
                
                <!-- 权限列表 -->
                <div id="tableContainer">
                    <div class="loading">
                        <i class="fas fa-spinner fa-spin"></i>
                        正在加载权限数据...
                    </div>
                </div>
            </div>
        `;
        return page;
    }
    
    /**
     * 绑定事件
     */
    bindEvents() {
        // 添加权限
        this.element.querySelector('#addPermissionBtn').addEventListener('click', () => {
            this.handleAdd();
        });
        
        // 刷新
        this.element.querySelector('#refreshPermissionBtn').addEventListener('click', () => {
            this.refresh();
        });
        
        // 搜索
        this.element.querySelector('#searchPermissionBtn').addEventListener('click', () => {
            this.performSearch();
        });
        
        // 重置搜索
        this.element.querySelector('#resetPermissionSearchBtn').addEventListener('click', () => {
            this.resetSearch();
        });
    }
    
    /**
     * 获取表格列配置
     */
    getColumns() {
        return [
            { key: 'id', title: 'ID', width: '80px' },
            { key: 'url', title: '权限链接' },
            { key: 'description', title: '权限描述' },
            { key: 'createDate', title: '创建时间', type: 'date' },
            { key: 'createBy', title: '创建者' }
        ];
    }
    
    /**
     * 获取表格操作按钮配置
     */
    getActions() {
        return [
            {
                text: '编辑',
                className: 'btn-primary',
                icon: 'fas fa-edit',
                handler: (permission) => this.handleEdit(permission)
            },
            {
                text: '分配角色',
                className: 'btn-info',
                icon: 'fas fa-user-tag',
                handler: (permission) => this.handleAssignRoles(permission)
            },
            {
                text: '删除',
                className: 'btn-danger',
                icon: 'fas fa-trash',
                handler: (permission) => this.handleDelete(permission)
            }
        ];
    }
    
    /**
     * 加载数据
     */
    async loadData(searchParams = {}) {
        const queryData = {
            current: 1,
            size: 10,
            paramObj: searchParams
        };
        
        const result = await API.post(CONFIG.API_ENDPOINTS.PERMISSION.QUERY, queryData);
        
        if (result && result.records) {
            return result.records || [];
        } else {
            throw new Error(result?.message || '加载权限数据失败');
        }
    }
    
    /**
     * 初始化数据
     */
    async initData() {
        const data = await this.loadData();
        this.displayData(data);
    }
    
    /**
     * 执行搜索
     */
    performSearch() {
        const url = this.element.querySelector('#permissionUrlSearch').value.trim();
        const description = this.element.querySelector('#permissionDescSearch').value.trim();
        
        const searchParams = {};
        if (url) searchParams.url = url;
        if (description) searchParams.description = description;
        
        this.handleSearch(searchParams);
    }
    
    /**
     * 重置搜索
     */
    resetSearch() {
        this.element.querySelector('#permissionUrlSearch').value = '';
        this.element.querySelector('#permissionDescSearch').value = '';
        this.handleSearch({});
    }
    
    /**
     * 处理添加权限
     */
    handleAdd() {
        this.showPermissionModal();
    }
    
    /**
     * 处理编辑权限
     */
    handleEdit(permission) {
        this.showPermissionModal(permission);
    }
    
    /**
     * 处理删除权限
     */
    async handleDelete(permission) {
        const confirmed = await UI.showConfirm(
            '确认删除',
            `确定要删除权限"${permission.description}"吗？此操作不可恢复。`
        );
        
        if (!confirmed) return;
        
        try {
            const result = await API.post(CONFIG.API_ENDPOINTS.PERMISSION.DELETE_BY_ID, {
                id: permission.id
            });
            
            if (result && result.code === 200) {
                UI.showMessage('权限删除成功', 'success');
                this.refresh();
            } else {
                UI.showMessage(result?.message || '删除失败', 'error');
            }
        } catch (error) {
            Utils.log.error('删除权限错误:', error);
            UI.showMessage('网络错误，请稍后重试', 'error');
        }
    }
    
    /**
     * 处理分配角色
     */
    handleAssignRoles(permission) {
        // 这里可以打开角色分配模态框
        // 为了简化，暂时只显示消息
        UI.showMessage(`为权限"${permission.description}"分配角色功能开发中`, 'info');
    }
    
    /**
     * 显示权限编辑模态框
     */
    showPermissionModal(permission = null) {
        const isEdit = !!permission;
        const title = isEdit ? '编辑权限' : '添加权限';
        
        const modalContent = `
            <form id="permissionForm">
                <div class="form-group">
                    <label for="permissionUrl">权限链接 <span class="required">*</span></label>
                    <input type="text" id="permissionUrl" name="url" required 
                           value="${permission?.url || ''}" placeholder="请输入权限链接，如：/admin/queryUser">
                </div>
                <div class="form-group">
                    <label for="permissionDescription">权限描述 <span class="required">*</span></label>
                    <input type="text" id="permissionDescription" name="description" required 
                           value="${permission?.description || ''}" placeholder="请输入权限描述">
                </div>
                ${isEdit ? `<input type="hidden" name="id" value="${permission.id}">` : ''}
            </form>
        `;
        
        UI.showModal(title, modalContent, [
            {
                text: '取消',
                className: 'btn-secondary',
                handler: () => UI.hideModal()
            },
            {
                text: isEdit ? '更新' : '创建',
                className: 'btn-primary',
                handler: () => this.savePermission(isEdit)
            }
        ]);
    }
    
    /**
     * 保存权限
     */
    async savePermission(isEdit) {
        try {
            const form = document.getElementById('permissionForm');
            const formData = new FormData(form);
            const permissionData = Object.fromEntries(formData.entries());
            
            // 验证数据
            if (!permissionData.url || !permissionData.description) {
                UI.showMessage('请填写完整的权限信息', 'error');
                return;
            }
            
            const result = await API.post(CONFIG.API_ENDPOINTS.PERMISSION.MODIFY, permissionData);
            
            if (result && result.code === 200) {
                UI.hideModal();
                UI.showMessage(isEdit ? '权限更新成功' : '权限创建成功', 'success');
                this.refresh();
            } else {
                UI.showMessage(result?.message || '操作失败', 'error');
            }
        } catch (error) {
            Utils.log.error('保存权限错误:', error);
            UI.showMessage('网络错误，请稍后重试', 'error');
        }
    }
}

// 延迟注册页面，确保PageManager已初始化
setTimeout(() => {
    try {
        window.PageManager.registerPage('permission-management', async () => {
            const page = new PermissionManagementPage();
            return await page.show();
        });
        console.log('PermissionManagement页面注册成功');
    } catch (error) {
        console.error('PermissionManagement页面注册失败:', error);
    }
}, 200);
