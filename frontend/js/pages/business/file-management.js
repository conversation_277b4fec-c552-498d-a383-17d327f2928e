/**
 * 文件管理页面
 */
class FileManagementPage extends BasePage {
    constructor() {
        super('file-management', '文件管理');
        this.uploadedFiles = [];
    }
    
    /**
     * 创建页面元素
     */
    createElement() {
        const page = document.createElement('div');
        page.innerHTML = `
            <div class="file-management-container">
                <div class="file-header">
                    <h3 class="file-title">
                        <i class="fas fa-folder-open"></i>
                        文件管理
                    </h3>
                    <p class="file-description">
                        上传、下载和管理您的文件
                    </p>
                </div>
                
                <!-- 文件上传区域 -->
                <div class="upload-section">
                    <h4>
                        <i class="fas fa-cloud-upload-alt"></i>
                        文件上传
                    </h4>
                    <div class="file-upload-area" id="fileUploadArea">
                        <div class="file-upload-icon">
                            <i class="fas fa-cloud-upload-alt"></i>
                        </div>
                        <div class="file-upload-text">点击选择文件或拖拽文件到此处</div>
                        <div class="file-upload-hint">支持多种文件格式，单个文件最大 10MB</div>
                        <input type="file" id="fileInput" multiple style="display: none;">
                    </div>
                    
                    <div class="upload-actions">
                        <button class="btn btn-primary" id="selectFilesBtn">
                            <i class="fas fa-file-plus"></i>
                            选择文件
                        </button>
                        <button class="btn btn-success" id="uploadFilesBtn" disabled>
                            <i class="fas fa-upload"></i>
                            开始上传
                        </button>
                        <button class="btn btn-secondary" id="clearFilesBtn">
                            <i class="fas fa-times"></i>
                            清空列表
                        </button>
                    </div>
                </div>
                
                <!-- 待上传文件列表 -->
                <div class="pending-files-section" id="pendingFilesSection" style="display: none;">
                    <h4>
                        <i class="fas fa-clock"></i>
                        待上传文件
                        <span class="file-count" id="pendingFileCount">0</span>
                    </h4>
                    <div class="file-list" id="pendingFileList"></div>
                </div>
                
                <!-- 已上传文件列表 -->
                <div class="uploaded-files-section">
                    <h4>
                        <i class="fas fa-check-circle"></i>
                        已上传文件
                        <span class="file-count" id="uploadedFileCount">0</span>
                    </h4>
                    <div id="uploadedFilesContainer">
                        <div class="empty-state">
                            <i class="fas fa-folder-open"></i>
                            <h3>暂无已上传文件</h3>
                            <p>上传文件后将在此处显示</p>
                        </div>
                    </div>
                </div>
            </div>
        `;
        return page;
    }
    
    /**
     * 绑定事件
     */
    bindEvents() {
        const fileInput = this.element.querySelector('#fileInput');
        const uploadArea = this.element.querySelector('#fileUploadArea');
        
        // 选择文件按钮
        this.element.querySelector('#selectFilesBtn').addEventListener('click', () => {
            fileInput.click();
        });
        
        // 文件选择事件
        fileInput.addEventListener('change', (e) => {
            this.handleFileSelect(e.target.files);
        });
        
        // 拖拽上传
        uploadArea.addEventListener('click', () => {
            fileInput.click();
        });
        
        uploadArea.addEventListener('dragover', (e) => {
            e.preventDefault();
            uploadArea.classList.add('dragover');
        });
        
        uploadArea.addEventListener('dragleave', () => {
            uploadArea.classList.remove('dragover');
        });
        
        uploadArea.addEventListener('drop', (e) => {
            e.preventDefault();
            uploadArea.classList.remove('dragover');
            this.handleFileSelect(e.dataTransfer.files);
        });
        
        // 上传文件按钮
        this.element.querySelector('#uploadFilesBtn').addEventListener('click', () => {
            this.uploadFiles();
        });
        
        // 清空文件列表
        this.element.querySelector('#clearFilesBtn').addEventListener('click', () => {
            this.clearPendingFiles();
        });
    }
    
    /**
     * 处理文件选择
     */
    handleFileSelect(files) {
        if (!files || files.length === 0) return;
        
        const fileArray = Array.from(files);
        const validFiles = fileArray.filter(file => this.validateFile(file));
        
        if (validFiles.length > 0) {
            this.addPendingFiles(validFiles);
            this.updateUploadButton();
        }
    }
    
    /**
     * 验证文件
     */
    validateFile(file) {
        const maxSize = 10 * 1024 * 1024; // 10MB
        
        if (file.size > maxSize) {
            UI.showMessage(`文件 "${file.name}" 超过最大限制 10MB`, 'warning');
            return false;
        }
        
        return true;
    }
    
    /**
     * 添加待上传文件
     */
    addPendingFiles(files) {
        const pendingSection = this.element.querySelector('#pendingFilesSection');
        const pendingList = this.element.querySelector('#pendingFileList');
        const countElement = this.element.querySelector('#pendingFileCount');
        
        files.forEach(file => {
            const fileItem = document.createElement('div');
            fileItem.className = 'file-item';
            fileItem.innerHTML = `
                <div class="file-info">
                    <i class="fas fa-file"></i>
                    <div>
                        <div class="file-name">${file.name}</div>
                        <div class="file-size">${Utils.formatFileSize(file.size)}</div>
                    </div>
                </div>
                <div class="file-actions">
                    <button class="btn btn-sm btn-danger" onclick="this.closest('.file-item').remove(); window.fileManagementPage.updateUploadButton();">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
            `;
            
            // 存储文件对象
            fileItem._fileObject = file;
            pendingList.appendChild(fileItem);
        });
        
        // 显示待上传区域
        pendingSection.style.display = 'block';
        
        // 更新计数
        const totalCount = pendingList.children.length;
        countElement.textContent = totalCount;
    }
    
    /**
     * 清空待上传文件
     */
    clearPendingFiles() {
        const pendingSection = this.element.querySelector('#pendingFilesSection');
        const pendingList = this.element.querySelector('#pendingFileList');
        const countElement = this.element.querySelector('#pendingFileCount');
        
        pendingList.innerHTML = '';
        pendingSection.style.display = 'none';
        countElement.textContent = '0';
        
        this.updateUploadButton();
    }
    
    /**
     * 更新上传按钮状态
     */
    updateUploadButton() {
        const uploadBtn = this.element.querySelector('#uploadFilesBtn');
        const pendingList = this.element.querySelector('#pendingFileList');
        
        uploadBtn.disabled = pendingList.children.length === 0;
        
        // 存储页面实例以供全局访问
        window.fileManagementPage = this;
    }
    
    /**
     * 上传文件
     */
    async uploadFiles() {
        const pendingList = this.element.querySelector('#pendingFileList');
        const fileItems = Array.from(pendingList.children);
        
        if (fileItems.length === 0) {
            UI.showMessage('请先选择要上传的文件', 'warning');
            return;
        }
        
        try {
            UI.showLoading('正在上传文件...');
            
            // 创建FormData
            const formData = new FormData();
            fileItems.forEach((item, index) => {
                if (item._fileObject) {
                    formData.append('files', item._fileObject);
                }
            });
            
            const result = await API.upload(CONFIG.API_ENDPOINTS.FILE.UPLOAD, formData);
            
            if (result && result.code === 200) {
                UI.showMessage(`成功上传 ${fileItems.length} 个文件`, 'success');
                this.clearPendingFiles();
                this.loadUploadedFiles();
            } else {
                UI.showMessage(result?.message || '文件上传失败', 'error');
            }
        } catch (error) {
            Utils.log.error('文件上传错误:', error);
            UI.showMessage('网络错误，请稍后重试', 'error');
        } finally {
            UI.hideLoading();
        }
    }
    
    /**
     * 加载已上传文件列表
     */
    async loadUploadedFiles() {
        // 这里应该调用后端接口获取已上传文件列表
        // 由于后端可能没有提供文件列表接口，这里使用模拟数据
        const mockFiles = [
            {
                id: 1,
                name: 'document.pdf',
                size: 1024000,
                uploadTime: new Date().toISOString(),
                type: 'application/pdf'
            }
        ];
        
        this.displayUploadedFiles(mockFiles);
    }
    
    /**
     * 显示已上传文件
     */
    displayUploadedFiles(files) {
        const container = this.element.querySelector('#uploadedFilesContainer');
        const countElement = this.element.querySelector('#uploadedFileCount');
        
        countElement.textContent = files.length;
        
        if (!files || files.length === 0) {
            container.innerHTML = `
                <div class="empty-state">
                    <i class="fas fa-folder-open"></i>
                    <h3>暂无已上传文件</h3>
                    <p>上传文件后将在此处显示</p>
                </div>
            `;
            return;
        }
        
        const columns = [
            { key: 'name', title: '文件名' },
            { 
                key: 'size', 
                title: '文件大小',
                formatter: (value) => Utils.formatFileSize(value)
            },
            { 
                key: 'uploadTime', 
                title: '上传时间',
                type: 'date'
            }
        ];
        
        const actions = [
            {
                text: '下载',
                className: 'btn-primary',
                icon: 'fas fa-download',
                handler: (file) => this.downloadFile(file)
            },
            {
                text: '删除',
                className: 'btn-danger',
                icon: 'fas fa-trash',
                handler: (file) => this.deleteFile(file)
            }
        ];
        
        const table = UI.createTable({
            columns: columns,
            data: files,
            actions: actions
        });
        container.innerHTML = '';
        container.appendChild(table);
    }
    
    /**
     * 下载文件
     */
    downloadFile(file) {
        // 这里应该调用后端下载接口
        UI.showMessage(`下载文件 "${file.name}" 功能开发中`, 'info');
    }
    
    /**
     * 删除文件
     */
    async deleteFile(file) {
        const confirmed = await UI.showConfirm(
            '确认删除',
            `确定要删除文件"${file.name}"吗？此操作不可恢复。`
        );
        
        if (confirmed) {
            UI.showMessage(`删除文件 "${file.name}" 功能开发中`, 'info');
        }
    }
    
    /**
     * 初始化数据
     */
    async initData() {
        this.loadUploadedFiles();
    }
}

// 延迟注册页面，确保PageManager已初始化
setTimeout(() => {
    try {
        // 注册页面
        window.PageManager.registerPage('file-management', async () => {
            const page = new FileManagementPage();
            return await page.show();
        });

        // 注册文件上传页面（兼容旧的页面ID）
        window.PageManager.registerPage('file-upload', async () => {
            const page = new FileManagementPage();
            return await page.show();
        });

        // 注册文件下载页面（兼容旧的页面ID）
        window.PageManager.registerPage('file-download', async () => {
            const page = new FileManagementPage();
            return await page.show();
        });

        console.log('FileManagement页面注册成功');
    } catch (error) {
        console.error('FileManagement页面注册失败:', error);
    }
}, 200);
