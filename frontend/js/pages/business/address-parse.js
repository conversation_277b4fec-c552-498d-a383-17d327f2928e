/**
 * 地址解析页面
 */
class AddressParsePage extends BasePage {
    constructor() {
        super('address-parse', '地址解析');
        this.inputData = '';
        this.parseResults = [];
    }
    
    /**
     * 创建页面元素
     */
    createElement() {
        const page = document.createElement('div');
        page.innerHTML = `
            <div class="address-parse-container">
                <div class="parse-header">
                    <h3 class="parse-title">
                        <i class="fas fa-map-marker-alt"></i>
                        地址解析工具
                    </h3>
                    <p class="parse-description">
                        智能解析地址信息，提取姓名、电话、省市区、详细地址等结构化数据
                    </p>
                </div>

                <!-- 地址输入区域 -->
                <div class="input-section">
                    <h4>
                        <i class="fas fa-edit"></i>
                        地址输入
                    </h4>
                    <div class="input-form">
                        <div class="form-group">
                            <label for="addressInput">请输入地址信息：</label>
                            <textarea id="addressInput" class="form-control" rows="6"
                                placeholder="请输入需要解析的地址信息，系统将自动识别其中的省市字段：&#10;&#10;示例：&#10;北京市朝阳区建国门外大街1号&#10;上海市浦东新区陆家嘴环路1000号&#10;广州市天河区珠江新城&#10;深圳市南山区科技园&#10;&#10;支持多行输入，每行一个地址"></textarea>
                        </div>
                        <div class="input-actions">
                            <button class="btn btn-primary" id="parseAddressBtn">
                                <i class="fas fa-search"></i>
                                解析地址
                            </button>
                            <button class="btn btn-secondary" id="loadTestDataBtn">
                                <i class="fas fa-download"></i>
                                加载测试数据
                            </button>
                            <button class="btn btn-warning" id="clearInputBtn">
                                <i class="fas fa-eraser"></i>
                                清空输入
                            </button>
                        </div>
                    </div>
                </div>

                <!-- 解析结果区域 -->
                <div class="result-section">
                    <h4>
                        <i class="fas fa-list"></i>
                        解析结果
                        <span class="data-count" id="resultCount">0</span>
                    </h4>
                    <div class="result-actions">
                        <button class="btn btn-info" id="exportResultBtn" disabled>
                            <i class="fas fa-file-export"></i>
                            导出结果
                        </button>
                        <button class="btn btn-danger" id="clearResultBtn" disabled>
                            <i class="fas fa-trash"></i>
                            清空结果
                        </button>
                    </div>
                    <div id="resultTableContainer">
                        <div class="empty-state">
                            <i class="fas fa-search"></i>
                            <h3>暂无解析结果</h3>
                            <p>请在上方输入地址信息并点击"解析地址"按钮</p>
                        </div>
                    </div>
                </div>
            </div>
        `;
        return page;
    }
    
    /**
     * 绑定事件
     */
    bindEvents() {
        // 解析地址
        this.element.querySelector('#parseAddressBtn').addEventListener('click', () => {
            this.parseAddress();
        });

        // 加载测试数据
        this.element.querySelector('#loadTestDataBtn').addEventListener('click', () => {
            this.loadTestData();
        });

        // 清空输入
        this.element.querySelector('#clearInputBtn').addEventListener('click', () => {
            this.clearInput();
        });

        // 导出结果
        this.element.querySelector('#exportResultBtn').addEventListener('click', () => {
            this.exportResults();
        });

        // 清空结果
        this.element.querySelector('#clearResultBtn').addEventListener('click', () => {
            this.clearResults();
        });
    }
    
    /**
     * 加载测试数据
     */
    async loadTestData() {
        try {
            const result = await API.get(CONFIG.API_ENDPOINTS.ADDRESS.TEST_DATA);

            if (result && result.code === 200) {
                const testData = result.obj || [];

                // 将测试数据格式化为输入文本（只保留地址部分）
                const inputText = testData.map(item => {
                    return item.address || item.name || '';
                }).filter(addr => addr.trim()).join('\n');

                // 填充到输入框
                this.element.querySelector('#addressInput').value = inputText;

                UI.showMessage(`成功加载 ${testData.length} 条测试数据`, 'success');
            } else {
                UI.showMessage(result?.message || '加载测试数据失败', 'error');
            }
        } catch (error) {
            Utils.log.error('加载测试数据错误:', error);
            UI.showMessage('网络错误，请稍后重试', 'error');
        }
    }
    
    /**
     * 解析地址
     */
    async parseAddress() {
        const input = this.element.querySelector('#addressInput').value.trim();

        if (!input) {
            UI.showMessage('请输入需要解析的地址信息', 'warning');
            return;
        }

        try {
            // 显示加载状态
            const container = this.element.querySelector('#resultTableContainer');
            container.innerHTML = '<div class="loading"><i class="fas fa-spinner fa-spin"></i> 正在解析地址...</div>';

            // 将输入文本按行分割
            const addresses = input.split('\n').filter(line => line.trim());

            const result = await API.post(CONFIG.API_ENDPOINTS.ADDRESS.PARSE, addresses);

            if (result && result.code === 200) {
                this.parseResults = result.obj || [];
                this.displayResults(this.parseResults);
                this.updateButtonStates();
                UI.showMessage(`成功解析 ${this.parseResults.length} 条地址`, 'success');
            } else {
                UI.showMessage(result?.message || '地址解析失败', 'error');
                container.innerHTML = `
                    <div class="error-state">
                        <i class="fas fa-exclamation-triangle"></i>
                        <h3>解析失败</h3>
                        <p>${result?.message || '地址解析失败，请检查输入格式'}</p>
                    </div>
                `;
            }
        } catch (error) {
            Utils.log.error('地址解析错误:', error);
            UI.showMessage('网络错误，请稍后重试', 'error');
            const container = this.element.querySelector('#resultTableContainer');
            container.innerHTML = `
                <div class="error-state">
                    <i class="fas fa-exclamation-triangle"></i>
                    <h3>网络错误</h3>
                    <p>请检查网络连接后重试</p>
                </div>
            `;
        }
    }

    /**
     * 清空输入
     */
    clearInput() {
        this.element.querySelector('#addressInput').value = '';
        UI.showMessage('输入已清空', 'info');
    }

    /**
     * 清空结果
     */
    clearResults() {
        this.parseResults = [];
        const container = this.element.querySelector('#resultTableContainer');
        container.innerHTML = `
            <div class="empty-state">
                <i class="fas fa-search"></i>
                <h3>暂无解析结果</h3>
                <p>请在上方输入地址信息并点击"解析地址"按钮</p>
            </div>
        `;
        this.updateButtonStates();
        UI.showMessage('结果已清空', 'info');
    }

    /**
     * 显示解析结果
     */
    displayResults(results) {
        const container = this.element.querySelector('#resultTableContainer');

        if (!results || results.length === 0) {
            container.innerHTML = `
                <div class="empty-state">
                    <i class="fas fa-search"></i>
                    <h3>暂无解析结果</h3>
                    <p>没有成功解析的地址信息</p>
                </div>
            `;
            return;
        }

        const columns = [
            { key: 'id', title: '序号' },
            { key: 'address', title: '原始地址' },
            { key: 'medicareProv', title: '识别省份' },
            { key: 'medicareCity', title: '识别城市' }
        ];

        const table = UI.createTable({
            columns: columns,
            data: results
        });
        container.innerHTML = '';
        container.appendChild(table);

        // 更新计数
        this.element.querySelector('#resultCount').textContent = results.length;
    }

    /**
     * 导出结果
     */
    exportResults() {
        if (!this.testData || this.testData.length === 0) {
            UI.showMessage('暂无数据可导出', 'warning');
            return;
        }
        
        try {
            // 转换数据为CSV格式
            const csvContent = this.convertToCSV(this.testData);
            
            // 创建下载链接
            const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
            const link = document.createElement('a');
            const url = URL.createObjectURL(blob);
            
            link.setAttribute('href', url);
            link.setAttribute('download', `address_parse_result_${Utils.formatDate(new Date(), 'YYYY-MM-DD_HH-mm-ss')}.csv`);
            link.style.visibility = 'hidden';
            
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
            
            UI.showMessage('导出成功', 'success');
        } catch (error) {
            Utils.log.error('导出错误:', error);
            UI.showMessage('导出失败', 'error');
        }
    }
    
    /**
     * 清空数据
     */
    clearData() {
        this.testData = [];
        this.displayAddressData([]);
        this.updateButtonStates();
        UI.showMessage('数据已清空', 'info');
    }
    
    /**
     * 显示地址数据
     */
    displayAddressData(data) {
        const container = this.element.querySelector('#addressTableContainer');
        const countElement = this.element.querySelector('#dataCount');
        
        // 更新数据计数
        countElement.textContent = data.length;
        
        if (!data || data.length === 0) {
            container.innerHTML = `
                <div class="empty-state">
                    <i class="fas fa-map-marker-alt"></i>
                    <h3>暂无地址数据</h3>
                    <p>点击"加载测试数据"按钮开始体验地址解析功能</p>
                </div>
            `;
            return;
        }
        
        const columns = [
            { key: 'name', title: '姓名', width: '100px' },
            { key: 'phone', title: '电话', width: '120px' },
            { key: 'originalAddress', title: '原始地址' },
            { key: 'province', title: '省份', width: '80px' },
            { key: 'city', title: '城市', width: '80px' },
            { key: 'district', title: '区县', width: '80px' },
            { key: 'detailAddress', title: '详细地址' }
        ];
        
        const table = UI.createTable({
            columns: columns,
            data: data
        });
        container.innerHTML = '';
        container.appendChild(table);
    }
    
    /**
     * 更新按钮状态
     */
    updateButtonStates() {
        const exportBtn = this.element.querySelector('#exportResultBtn');
        const clearResultBtn = this.element.querySelector('#clearResultBtn');

        const hasResults = this.parseResults && this.parseResults.length > 0;

        exportBtn.disabled = !hasResults;
        clearResultBtn.disabled = !hasResults;

        // 更新结果计数
        const resultCount = this.element.querySelector('#resultCount');
        if (resultCount) {
            resultCount.textContent = this.parseResults ? this.parseResults.length : 0;
        }
    }
    
    /**
     * 转换数据为CSV格式
     */
    convertToCSV(data) {
        if (!data || data.length === 0) return '';
        
        const headers = ['姓名', '电话', '原始地址', '省份', '城市', '区县', '详细地址'];
        const csvRows = [headers.join(',')];
        
        data.forEach(item => {
            const row = [
                item.name || '',
                item.phone || '',
                item.originalAddress || '',
                item.province || '',
                item.city || '',
                item.district || '',
                item.detailAddress || ''
            ].map(field => `"${field}"`);
            
            csvRows.push(row.join(','));
        });
        
        return '\uFEFF' + csvRows.join('\n'); // 添加BOM以支持中文
    }
}

// 延迟注册页面，确保PageManager已初始化
setTimeout(() => {
    try {
        // 注册页面
        window.PageManager.registerPage('address-parse', async () => {
            const page = new AddressParsePage();
            return await page.show();
        });

        // 注册地址测试页面（兼容旧的页面ID）
        window.PageManager.registerPage('address-test', async () => {
            const page = new AddressParsePage();
            return await page.show();
        });

        console.log('AddressParse页面注册成功');
    } catch (error) {
        console.error('AddressParse页面注册失败:', error);
    }
}, 200);
