/**
 * PageManager降级处理
 * 如果新的PageManager有问题，使用这个简化版本
 */

// 检查是否需要降级处理
if (!window.PageManager ||
    typeof window.PageManager.showPage !== 'function' ||
    typeof window.PageManager.registerPage !== 'function') {
    console.warn('PageManager不可用，启用降级处理...');
    
    // 创建简化的PageManager
    window.PageManager = {
        pages: new Map(),
        currentPage: null,
        
        /**
         * 注册页面
         */
        registerPage: function(pageId, createFunction) {
            console.log(`[Fallback] 注册页面: ${pageId}`);
            this.pages.set(pageId, createFunction);
        },
        
        /**
         * 显示页面
         */
        showPage: function(pageId) {
            console.log(`[Fallback] 显示页面: ${pageId}`);
            
            try {
                const mainContent = document.getElementById('mainContent');
                if (!mainContent) {
                    console.error('未找到主内容容器 #mainContent');
                    return;
                }
                
                // 查找现有页面元素
                let pageElement = document.getElementById(pageId);
                
                if (!pageElement) {
                    // 页面不存在，创建新页面
                    const createFunction = this.pages.get(pageId);
                    if (createFunction) {
                        try {
                            const result = createFunction();
                            
                            // 处理异步创建函数
                            if (result && typeof result.then === 'function') {
                                result.then(element => {
                                    if (element) {
                                        element.id = pageId;
                                        element.className = 'page';
                                        this.displayPage(element, pageId);
                                    }
                                }).catch(error => {
                                    console.error(`[Fallback] 异步创建页面失败: ${pageId}`, error);
                                });
                                return;
                            } else if (result) {
                                pageElement = result;
                                pageElement.id = pageId;
                                pageElement.className = 'page';
                                mainContent.appendChild(pageElement);
                            }
                        } catch (error) {
                            console.error(`[Fallback] 创建页面失败: ${pageId}`, error);
                            return;
                        }
                    } else {
                        console.error(`[Fallback] 页面不存在: ${pageId}`);
                        return;
                    }
                }
                
                this.displayPage(pageElement, pageId);
                
            } catch (error) {
                console.error(`[Fallback] 显示页面错误: ${pageId}`, error);
            }
        },
        
        /**
         * 显示页面元素
         */
        displayPage: function(pageElement, pageId) {
            const mainContent = document.getElementById('mainContent');
            if (!mainContent) return;
            
            // 隐藏所有页面
            const allPages = mainContent.querySelectorAll('.page');
            allPages.forEach(page => {
                page.classList.remove('active');
                page.style.display = 'none';
            });
            
            // 显示目标页面
            pageElement.classList.add('active');
            pageElement.style.display = 'block';
            this.currentPage = pageId;
            
            // 更新侧边栏激活状态
            this.updateSidebarActive(pageId);
            
            console.log(`[Fallback] 页面显示完成: ${pageId}`);
        },
        
        /**
         * 更新侧边栏激活状态
         */
        updateSidebarActive: function(pageId) {
            const sidebarLinks = document.querySelectorAll('.sidebar a');
            sidebarLinks.forEach(link => {
                link.classList.remove('active');
                if (link.dataset.page === pageId) {
                    link.classList.add('active');
                }
            });
        },
        
        /**
         * 获取当前页面
         */
        getCurrentPage: function() {
            return this.currentPage;
        }
    };
    
    console.log('PageManager降级处理已启用');
    console.log('PageManager降级处理已启用');
} else {
    console.log('PageManager正常工作，无需降级处理');
}

// 最终验证
setTimeout(() => {
    console.log('PageManager最终状态:', {
        exists: !!window.PageManager,
        registerPage: typeof window.PageManager?.registerPage,
        showPage: typeof window.PageManager?.showPage,
        pages: window.PageManager?.pages?.size || 0
    });
}, 100);
