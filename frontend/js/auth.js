/**
 * 认证管理文件
 * 这个文件处理用户登录、登出、权限验证等认证相关功能
 */

// 认证管理类
class AuthManager {
    constructor() {
        this.currentUser = null;
        this.token = null;
        this.init();
    }
    
    /**
     * 初始化认证管理器
     */
    init() {
        // 从本地存储恢复登录状态 - token直接获取字符串，用户信息解析JSON
        this.token = localStorage.getItem(CONFIG.CONSTANTS.STORAGE_KEYS.TOKEN);
        this.currentUser = Utils.storage.get(CONFIG.CONSTANTS.STORAGE_KEYS.USER_INFO);
        
        // 如果有token，验证其有效性
        if (this.token) {
            this.validateToken();
        }
    }
    
    /**
     * 用户登录
     * @param {Object} credentials - 登录凭据 {username, password}
     * @returns {Promise<boolean>} 登录是否成功
     */
    async login(credentials) {
        try {
            Utils.log.debug('开始登录:', credentials.username);
            
            // 显示加载状态
            UI.showLoading();
            
            // 调用登录API
            const response = await API.auth.login(credentials);
            
            Utils.log.debug('登录响应:', response);
            
            // 检查响应格式
            if (response && response.code === 200) {
                // 先清除旧的token
                localStorage.removeItem(CONFIG.CONSTANTS.STORAGE_KEYS.TOKEN);

                // 登录成功 - 从LoginResultDo对象中提取token字符串
                // 后端返回的data是LoginResultDo对象，包含token字段
                if (typeof response.data === 'object' && response.data.token) {
                    this.token = response.data.token;
                } else if (typeof response.data === 'string') {
                    this.token = response.data;
                } else {
                    Utils.log.error('无法从响应中提取token:', response.data);
                    UI.showMessage('登录响应格式错误', 'error');
                    return false;
                }
                Utils.log.debug('登录成功，获得token:', this.token);

                // 构造用户信息（从响应中获取或使用默认值）
                this.currentUser = {
                    username: credentials.username,
                    loginTime: new Date().toISOString(),
                    // 可以从响应中获取更多用户信息
                    ...response.userInfo
                };

                // 保存到本地存储 - token直接存储为字符串，用户信息存储为JSON
                Utils.log.debug('保存token到localStorage:', this.token);
                localStorage.setItem(CONFIG.CONSTANTS.STORAGE_KEYS.TOKEN, this.token);
                Utils.storage.set(CONFIG.CONSTANTS.STORAGE_KEYS.USER_INFO, this.currentUser);

                // 验证token是否正确保存
                const savedToken = localStorage.getItem(CONFIG.CONSTANTS.STORAGE_KEYS.TOKEN);
                Utils.log.debug('验证保存的token:', savedToken);
                
                // 更新UI
                this.updateAuthUI();
                
                // 显示成功消息
                UI.showMessage('登录成功！', 'success');
                
                Utils.log.info('用户登录成功:', this.currentUser.username);
                
                return true;
                
            } else {
                // 登录失败
                const errorMsg = response?.message || '登录失败，请检查用户名和密码';
                UI.showMessage(errorMsg, 'error');
                Utils.log.warn('登录失败:', errorMsg);
                return false;
            }
            
        } catch (error) {
            Utils.log.error('登录错误:', error);
            UI.showMessage(error.message || '登录失败，请稍后重试', 'error');
            return false;
        } finally {
            UI.hideLoading();
        }
    }
    
    /**
     * 用户登出
     * @returns {Promise<boolean>} 登出是否成功
     */
    async logout() {
        try {
            Utils.log.debug('开始登出');
            
            // 如果有token，调用登出API
            if (this.token) {
                try {
                    await API.auth.logout();
                } catch (error) {
                    // 即使API调用失败，也要清除本地状态
                    Utils.log.warn('登出API调用失败:', error);
                }
            }
            
            // 清除本地状态
            this.clearAuthState();
            
            // 更新UI
            this.updateAuthUI();
            
            // 跳转到登录页
            App.showPage(CONFIG.ROUTES.LOGIN);
            
            UI.showMessage('已安全退出', 'info');
            
            Utils.log.info('用户已登出');
            
            return true;
            
        } catch (error) {
            Utils.log.error('登出错误:', error);
            
            // 即使出错也要清除本地状态
            this.clearAuthState();
            this.updateAuthUI();
            App.showPage(CONFIG.ROUTES.LOGIN);
            
            return false;
        }
    }
    
    /**
     * 清除认证状态
     */
    clearAuthState() {
        this.token = null;
        this.currentUser = null;
        
        // 清除本地存储
        localStorage.removeItem(CONFIG.CONSTANTS.STORAGE_KEYS.TOKEN);
        Utils.storage.remove(CONFIG.CONSTANTS.STORAGE_KEYS.USER_INFO);
    }
    
    /**
     * 验证token有效性
     */
    async validateToken() {
        if (!this.token) {
            return false;
        }
        
        try {
            // 这里可以调用一个验证token的API
            // 暂时使用简单的过期时间检查
            const tokenData = this.parseToken(this.token);
            
            if (tokenData && tokenData.exp && tokenData.exp * 1000 > Date.now()) {
                // token有效
                this.updateAuthUI();
                return true;
            } else {
                // token过期
                this.clearAuthState();
                return false;
            }
            
        } catch (error) {
            Utils.log.error('Token验证失败:', error);
            this.clearAuthState();
            return false;
        }
    }
    
    /**
     * 解析JWT token（简单实现）
     * @param {string} token - JWT token
     * @returns {Object|null} 解析后的数据
     */
    parseToken(token) {
        try {
            const parts = token.split('.');
            if (parts.length !== 3) {
                return null;
            }
            
            const payload = parts[1];
            const decoded = atob(payload.replace(/-/g, '+').replace(/_/g, '/'));
            return JSON.parse(decoded);
            
        } catch (error) {
            Utils.log.error('Token解析失败:', error);
            return null;
        }
    }
    
    /**
     * 检查是否已登录
     * @returns {boolean} 是否已登录
     */
    isAuthenticated() {
        return !!(this.token && this.currentUser);
    }
    
    /**
     * 获取当前用户信息
     * @returns {Object|null} 用户信息
     */
    getCurrentUser() {
        return this.currentUser;
    }
    
    /**
     * 获取当前token
     * @returns {string|null} token
     */
    getToken() {
        return this.token;
    }
    
    /**
     * 检查用户权限（简单实现）
     * @param {string} permission - 权限标识
     * @returns {boolean} 是否有权限
     */
    hasPermission(permission) {
        if (!this.isAuthenticated()) {
            return false;
        }
        
        // 这里可以实现更复杂的权限检查逻辑
        // 暂时返回true，表示已登录用户有所有权限
        return true;
    }
    
    /**
     * 检查用户角色
     * @param {string} role - 角色标识
     * @returns {boolean} 是否有该角色
     */
    hasRole(role) {
        if (!this.isAuthenticated()) {
            return false;
        }
        
        // 这里可以实现角色检查逻辑
        // 暂时返回true
        return true;
    }
    
    /**
     * 更新认证相关的UI
     */
    updateAuthUI() {
        const userInfoElement = document.getElementById('currentUser');
        const logoutBtn = document.getElementById('logoutBtn');
        const sidebar = document.getElementById('sidebar');
        
        if (this.isAuthenticated()) {
            // 已登录状态
            if (userInfoElement) {
                userInfoElement.textContent = this.currentUser.username;
            }
            
            if (logoutBtn) {
                logoutBtn.style.display = 'flex';
            }
            
            if (sidebar) {
                sidebar.style.display = 'block';
            }
            
        } else {
            // 未登录状态
            if (userInfoElement) {
                userInfoElement.textContent = '未登录';
            }
            
            if (logoutBtn) {
                logoutBtn.style.display = 'none';
            }
            
            if (sidebar) {
                sidebar.style.display = 'none';
            }
        }
    }
    
    /**
     * 权限守卫 - 检查页面访问权限
     * @param {string} page - 页面标识
     * @returns {boolean} 是否允许访问
     */
    canAccessPage(page) {
        // 登录页面总是可以访问
        if (page === CONFIG.ROUTES.LOGIN) {
            return true;
        }
        
        // 其他页面需要登录
        if (!this.isAuthenticated()) {
            // 未登录，跳转到登录页
            App.showPage(CONFIG.ROUTES.LOGIN);
            UI.showMessage('请先登录', 'warning');
            return false;
        }
        
        // 这里可以添加更细粒度的权限检查
        // 例如：某些页面需要特定权限
        
        return true;
    }
}

// 创建认证管理器实例
const Auth = new AuthManager();

// 全局认证对象
window.Auth = Auth;
