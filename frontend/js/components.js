/**
 * UI组件文件
 * 这个文件包含了可复用的UI组件和界面操作函数
 */

// UI组件管理类
class UIManager {
    constructor() {
        this.messageContainer = null;
        this.loadingOverlay = null;
        this.init();
    }
    
    /**
     * 初始化UI管理器
     */
    init() {
        this.messageContainer = document.getElementById('messageContainer');
        this.loadingOverlay = document.getElementById('loadingOverlay');
    }
    
    /**
     * 显示消息提示
     * @param {string} message - 消息内容
     * @param {string} type - 消息类型 (success, error, warning, info)
     * @param {number} duration - 显示时长（毫秒）
     */
    showMessage(message, type = 'info', duration = CONFIG.CONSTANTS.MESSAGE.DURATION) {
        if (!this.messageContainer) {
            console.warn('消息容器未找到');
            return;
        }
        
        // 创建消息元素
        const messageElement = document.createElement('div');
        messageElement.className = `message ${type}`;
        
        // 根据类型选择图标
        const icons = {
            success: 'fas fa-check-circle',
            error: 'fas fa-exclamation-circle',
            warning: 'fas fa-exclamation-triangle',
            info: 'fas fa-info-circle'
        };
        
        messageElement.innerHTML = `
            <i class="${icons[type] || icons.info}"></i>
            <span>${Utils.escapeHtml(message)}</span>
        `;
        
        // 添加到容器
        this.messageContainer.appendChild(messageElement);
        
        // 自动移除
        setTimeout(() => {
            if (messageElement.parentNode) {
                messageElement.style.animation = 'slideOut 0.3s ease forwards';
                setTimeout(() => {
                    messageElement.remove();
                }, 300);
            }
        }, duration);
    }
    
    /**
     * 显示加载状态
     * @param {string} text - 加载文本
     */
    showLoading(text = '加载中...') {
        if (this.loadingOverlay) {
            const textElement = this.loadingOverlay.querySelector('p');
            if (textElement) {
                textElement.textContent = text;
            }
            this.loadingOverlay.classList.add('show');
        }
    }
    
    /**
     * 隐藏加载状态
     */
    hideLoading() {
        if (this.loadingOverlay) {
            this.loadingOverlay.classList.remove('show');
        }
    }
    
    /**
     * 创建模态框
     * @param {Object} options - 模态框选项
     * @returns {HTMLElement} 模态框元素
     */
    createModal(options = {}) {
        const {
            title = '提示',
            content = '',
            size = 'medium',
            closable = true,
            onClose = null
        } = options;
        
        // 创建模态框结构
        const overlay = document.createElement('div');
        overlay.className = 'modal-overlay';
        
        const modal = document.createElement('div');
        modal.className = `modal modal-${size}`;
        
        const header = document.createElement('div');
        header.className = 'modal-header';
        header.innerHTML = `
            <h3 class="modal-title">${Utils.escapeHtml(title)}</h3>
            ${closable ? '<button class="modal-close"><i class="fas fa-times"></i></button>' : ''}
        `;
        
        const body = document.createElement('div');
        body.className = 'modal-body';
        if (typeof content === 'string') {
            body.innerHTML = content;
        } else {
            body.appendChild(content);
        }
        
        modal.appendChild(header);
        modal.appendChild(body);
        overlay.appendChild(modal);
        
        // 添加关闭事件
        if (closable) {
            const closeBtn = header.querySelector('.modal-close');
            const closeModal = () => {
                overlay.classList.remove('show');
                setTimeout(() => {
                    overlay.remove();
                    if (onClose) onClose();
                }, 300);
            };
            
            closeBtn.addEventListener('click', closeModal);
            overlay.addEventListener('click', (e) => {
                if (e.target === overlay) {
                    closeModal();
                }
            });
        }
        
        // 添加到页面
        document.body.appendChild(overlay);
        
        // 显示模态框
        setTimeout(() => {
            overlay.classList.add('show');
        }, 10);
        
        return overlay;
    }
    
    /**
     * 显示确认对话框（简化接口）
     * @param {string} title - 标题
     * @param {string} message - 消息内容
     * @returns {Promise<boolean>} 用户选择结果
     */
    showConfirm(title, message) {
        return this.confirm({
            title: title,
            message: message
        });
    }

    /**
     * 显示模态对话框
     * @param {string} title - 标题
     * @param {string} content - 内容HTML
     * @param {Array} buttons - 按钮配置
     */
    showModal(title, content, buttons = []) {
        // 移除已存在的模态框
        this.hideModal();

        // 创建模态框HTML
        const modalHtml = `
            <div class="modal-overlay" id="modalOverlay">
                <div class="modal-dialog">
                    <div class="modal-header">
                        <h5 class="modal-title">${title}</h5>
                        <button type="button" class="modal-close" onclick="UI.hideModal()">
                            <i class="fas fa-times"></i>
                        </button>
                    </div>
                    <div class="modal-body">
                        ${content}
                    </div>
                    <div class="modal-footer">
                        ${buttons.map(btn => `
                            <button type="button" class="btn ${btn.className || 'btn-secondary'}"
                                    onclick="(${btn.handler.toString()})()">
                                ${btn.text}
                            </button>
                        `).join('')}
                    </div>
                </div>
            </div>
        `;

        // 添加到页面
        document.body.insertAdjacentHTML('beforeend', modalHtml);

        // 添加样式（如果还没有）
        if (!document.getElementById('modalStyles')) {
            const styles = `
                <style id="modalStyles">
                .modal-overlay {
                    position: fixed;
                    top: 0;
                    left: 0;
                    width: 100%;
                    height: 100%;
                    background: rgba(0,0,0,0.5);
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    z-index: 1000;
                }
                .modal-dialog {
                    background: white;
                    border-radius: 8px;
                    max-width: 500px;
                    width: 90%;
                    max-height: 90vh;
                    overflow-y: auto;
                    box-shadow: 0 4px 20px rgba(0,0,0,0.3);
                }
                .modal-header {
                    padding: 15px 20px;
                    border-bottom: 1px solid #dee2e6;
                    display: flex;
                    justify-content: space-between;
                    align-items: center;
                }
                .modal-title {
                    margin: 0;
                    font-size: 1.25rem;
                }
                .modal-close {
                    background: none;
                    border: none;
                    font-size: 1.2rem;
                    cursor: pointer;
                    color: #6c757d;
                }
                .modal-close:hover {
                    color: #000;
                }
                .modal-body {
                    padding: 20px;
                }
                .modal-footer {
                    padding: 15px 20px;
                    border-top: 1px solid #dee2e6;
                    display: flex;
                    justify-content: flex-end;
                    gap: 10px;
                }
                </style>
            `;
            document.head.insertAdjacentHTML('beforeend', styles);
        }
    }

    /**
     * 隐藏模态对话框
     */
    hideModal() {
        const modal = document.getElementById('modalOverlay');
        if (modal) {
            modal.remove();
        }
    }

    /**
     * 创建确认对话框
     * @param {Object} options - 对话框选项
     * @returns {Promise<boolean>} 用户选择结果
     */
    confirm(options = {}) {
        const {
            title = '确认',
            message = '确定要执行此操作吗？',
            confirmText = '确定',
            cancelText = '取消',
            type = 'warning'
        } = options;
        
        return new Promise((resolve) => {
            const content = document.createElement('div');
            content.innerHTML = `
                <div class="confirm-content">
                    <div class="confirm-icon ${type}">
                        <i class="fas fa-${type === 'danger' ? 'exclamation-triangle' : 'question-circle'}"></i>
                    </div>
                    <p class="confirm-message">${Utils.escapeHtml(message)}</p>
                    <div class="confirm-actions">
                        <button class="btn btn-secondary cancel-btn">${Utils.escapeHtml(cancelText)}</button>
                        <button class="btn btn-${type === 'danger' ? 'danger' : 'primary'} confirm-btn">${Utils.escapeHtml(confirmText)}</button>
                    </div>
                </div>
            `;
            
            const modal = this.createModal({
                title,
                content,
                closable: false
            });
            
            // 添加按钮事件
            const confirmBtn = content.querySelector('.confirm-btn');
            const cancelBtn = content.querySelector('.cancel-btn');
            
            confirmBtn.addEventListener('click', () => {
                modal.remove();
                resolve(true);
            });
            
            cancelBtn.addEventListener('click', () => {
                modal.remove();
                resolve(false);
            });
        });
    }
    
    /**
     * 创建表格
     * @param {Object} options - 表格选项
     * @returns {HTMLElement} 表格容器元素
     */
    createTable(options = {}) {
        const {
            columns = [],
            data = [],
            actions = [],
            pagination = false,
            searchable = false,
            title = ''
        } = options;
        
        const container = document.createElement('div');
        container.className = 'table-container';
        
        // 表格头部
        const header = document.createElement('div');
        header.className = 'table-header';
        
        if (title) {
            const titleElement = document.createElement('h3');
            titleElement.className = 'table-title';
            titleElement.textContent = title;
            header.appendChild(titleElement);
        }
        
        // 搜索框
        if (searchable) {
            const searchContainer = document.createElement('div');
            searchContainer.className = 'search-container';
            searchContainer.innerHTML = `
                <i class="fas fa-search search-icon"></i>
                <input type="text" class="search-input" placeholder="搜索...">
            `;
            header.appendChild(searchContainer);
        }
        
        container.appendChild(header);
        
        // 表格
        const table = document.createElement('table');
        table.className = 'data-table';
        
        // 表头
        const thead = document.createElement('thead');
        const headerRow = document.createElement('tr');
        
        columns.forEach(column => {
            const th = document.createElement('th');
            th.textContent = column.title || column.key;
            if (column.width) {
                th.style.width = column.width;
            }
            headerRow.appendChild(th);
        });
        
        if (actions.length > 0) {
            const actionTh = document.createElement('th');
            actionTh.textContent = '操作';
            actionTh.style.textAlign = 'center';
            headerRow.appendChild(actionTh);
        }
        
        thead.appendChild(headerRow);
        table.appendChild(thead);
        
        // 表体
        const tbody = document.createElement('tbody');
        this.updateTableData(tbody, columns, data, actions);
        table.appendChild(tbody);
        
        container.appendChild(table);
        
        // 分页
        if (pagination) {
            const paginationContainer = document.createElement('div');
            paginationContainer.className = 'pagination';
            container.appendChild(paginationContainer);
        }
        
        return container;
    }
    
    /**
     * 更新表格数据
     * @param {HTMLElement} tbody - 表体元素
     * @param {Array} columns - 列配置
     * @param {Array} data - 数据
     * @param {Array} actions - 操作按钮
     */
    updateTableData(tbody, columns, data, actions = []) {
        tbody.innerHTML = '';
        
        if (data.length === 0) {
            const row = document.createElement('tr');
            const cell = document.createElement('td');
            cell.colSpan = columns.length + (actions.length > 0 ? 1 : 0);
            cell.textContent = '暂无数据';
            cell.style.textAlign = 'center';
            cell.style.padding = '40px';
            cell.style.color = '#999';
            row.appendChild(cell);
            tbody.appendChild(row);
            return;
        }
        
        data.forEach((item, index) => {
            const row = document.createElement('tr');
            
            // 数据列
            columns.forEach(column => {
                const cell = document.createElement('td');
                let value = item[column.key];
                
                // 格式化数据
                let isHtml = false;
                if (column.formatter && typeof column.formatter === 'function') {
                    value = column.formatter(value, item, index);
                    // formatter通常返回HTML字符串（如badge等）
                    isHtml = true;
                } else if (column.type === 'date' && value) {
                    value = Utils.formatDate(value);
                } else if (column.type === 'fileSize' && value) {
                    value = Utils.formatFileSize(value);
                }

                // 设置单元格内容
                if (isHtml && typeof value === 'string') {
                    cell.innerHTML = value;
                } else if (typeof value === 'string') {
                    cell.textContent = value;
                } else if (value instanceof Node) {
                    cell.appendChild(value);
                } else {
                    // 如果不是字符串也不是DOM节点，转换为字符串
                    cell.textContent = String(value || '');
                }
                
                row.appendChild(cell);
            });
            
            // 操作列
            if (actions.length > 0) {
                const actionCell = document.createElement('td');
                actionCell.className = 'actions';
                
                actions.forEach(action => {
                    const btn = document.createElement('button');
                    btn.className = `btn btn-sm btn-${action.type || 'primary'}`;
                    btn.innerHTML = `<i class="${action.icon}"></i> ${action.text}`;
                    btn.addEventListener('click', () => {
                        if (action.handler) {
                            action.handler(item, index);
                        }
                    });
                    actionCell.appendChild(btn);
                });
                
                row.appendChild(actionCell);
            }
            
            tbody.appendChild(row);
        });
    }
    
    /**
     * 创建表单
     * @param {Object} options - 表单选项
     * @returns {HTMLElement} 表单元素
     */
    createForm(options = {}) {
        const {
            fields = [],
            title = '',
            submitText = '提交',
            onSubmit = null
        } = options;
        
        const container = document.createElement('div');
        container.className = 'form-container';
        
        if (title) {
            const titleElement = document.createElement('h3');
            titleElement.className = 'form-title';
            titleElement.textContent = title;
            container.appendChild(titleElement);
        }
        
        const form = document.createElement('form');
        form.className = 'form';
        
        fields.forEach(field => {
            const formGroup = document.createElement('div');
            formGroup.className = 'form-group';
            
            // 标签
            if (field.label) {
                const label = document.createElement('label');
                label.className = 'form-label';
                label.textContent = field.label;
                if (field.required) {
                    label.innerHTML += ' <span style="color: red;">*</span>';
                }
                formGroup.appendChild(label);
            }
            
            // 输入控件
            let input;
            switch (field.type) {
                case 'textarea':
                    input = document.createElement('textarea');
                    break;
                case 'select':
                    input = document.createElement('select');
                    if (field.options) {
                        field.options.forEach(option => {
                            const optionElement = document.createElement('option');
                            optionElement.value = option.value;
                            optionElement.textContent = option.text;
                            input.appendChild(optionElement);
                        });
                    }
                    break;
                default:
                    input = document.createElement('input');
                    input.type = field.type || 'text';
            }
            
            input.className = 'form-control';
            input.name = field.name;
            if (field.placeholder) input.placeholder = field.placeholder;
            if (field.required) input.required = true;
            if (field.value) input.value = field.value;
            
            formGroup.appendChild(input);
            
            // 帮助文本
            if (field.help) {
                const helpText = document.createElement('div');
                helpText.className = 'form-text';
                helpText.textContent = field.help;
                formGroup.appendChild(helpText);
            }
            
            form.appendChild(formGroup);
        });
        
        // 提交按钮
        const submitBtn = document.createElement('button');
        submitBtn.type = 'submit';
        submitBtn.className = 'btn btn-primary';
        submitBtn.textContent = submitText;
        form.appendChild(submitBtn);
        
        // 提交事件
        if (onSubmit) {
            form.addEventListener('submit', (e) => {
                e.preventDefault();
                const formData = new FormData(form);
                const data = Object.fromEntries(formData.entries());
                onSubmit(data, form);
            });
        }
        
        container.appendChild(form);
        return container;
    }
}

// 创建UI管理器实例
const UI = new UIManager();

// 添加CSS动画
const style = document.createElement('style');
style.textContent = `
    @keyframes slideOut {
        to { transform: translateX(100%); opacity: 0; }
    }
    
    .confirm-content {
        text-align: center;
        padding: 20px;
    }
    
    .confirm-icon {
        font-size: 3rem;
        margin-bottom: 20px;
    }
    
    .confirm-icon.warning { color: #ffc107; }
    .confirm-icon.danger { color: #dc3545; }
    .confirm-icon.info { color: #17a2b8; }
    
    .confirm-message {
        font-size: 1.1rem;
        margin-bottom: 30px;
        color: #333;
    }
    
    .confirm-actions {
        display: flex;
        gap: 15px;
        justify-content: center;
    }
`;
document.head.appendChild(style);

// 全局UI对象
window.UI = UI;
