/**
 * 基础页面类
 * 所有业务页面都应该继承此类
 */
class BasePage {
    constructor(pageId, title) {
        this.pageId = pageId;
        this.title = title;
        this.element = null;
        this.isInitialized = false;
    }
    
    /**
     * 创建页面元素
     * 子类必须实现此方法
     */
    createElement() {
        throw new Error('子类必须实现 createElement 方法');
    }
    
    /**
     * 绑定事件
     * 子类可以重写此方法
     */
    bindEvents() {
        // 默认实现为空
    }
    
    /**
     * 初始化数据
     * 子类可以重写此方法
     */
    async initData() {
        // 默认实现为空
    }
    
    /**
     * 获取页面元素
     */
    getElement() {
        if (!this.element) {
            this.element = this.createElement();
            this.element.id = this.pageId;
            this.element.className = 'page';
            this.bindEvents();
        }
        return this.element;
    }
    
    /**
     * 显示页面
     */
    async show() {
        const element = this.getElement();
        
        if (!this.isInitialized) {
            try {
                await this.initData();
                this.isInitialized = true;
            } catch (error) {
                Utils.log.error(`页面初始化失败: ${this.pageId}`, error);
                UI.showMessage('页面初始化失败', 'error');
            }
        }
        
        return element;
    }
    
    /**
     * 隐藏页面
     */
    hide() {
        if (this.element) {
            this.element.classList.remove('active');
        }
    }
    
    /**
     * 销毁页面
     */
    destroy() {
        if (this.element) {
            this.element.remove();
            this.element = null;
            this.isInitialized = false;
        }
    }
    
    /**
     * 刷新页面
     */
    async refresh() {
        try {
            await this.initData();
        } catch (error) {
            Utils.log.error(`页面刷新失败: ${this.pageId}`, error);
            UI.showMessage('页面刷新失败', 'error');
        }
    }
}

/**
 * 表格页面基类
 * 用于需要展示表格数据的页面
 */
class BaseTablePage extends BasePage {
    constructor(pageId, title, options = {}) {
        super(pageId, title);
        this.options = {
            searchable: true,
            addable: true,
            editable: true,
            deletable: true,
            ...options
        };
        this.currentData = [];
        this.searchParams = {};
    }
    
    /**
     * 获取表格列配置
     * 子类必须实现此方法
     */
    getColumns() {
        throw new Error('子类必须实现 getColumns 方法');
    }
    
    /**
     * 获取表格操作按钮配置
     * 子类可以重写此方法
     */
    getActions() {
        const actions = [];
        
        if (this.options.editable) {
            actions.push({
                text: '编辑',
                className: 'btn-primary',
                icon: 'fas fa-edit',
                handler: (item) => this.handleEdit(item)
            });
        }
        
        if (this.options.deletable) {
            actions.push({
                text: '删除',
                className: 'btn-danger',
                icon: 'fas fa-trash',
                handler: (item) => this.handleDelete(item)
            });
        }
        
        return actions;
    }
    
    /**
     * 加载数据
     * 子类必须实现此方法
     */
    async loadData(searchParams = {}) {
        throw new Error('子类必须实现 loadData 方法');
    }
    
    /**
     * 显示数据
     */
    displayData(data) {
        const container = this.element.querySelector('#tableContainer');
        if (!container) return;
        
        if (!data || data.length === 0) {
            container.innerHTML = this.getEmptyStateHtml();
            return;
        }
        
        const columns = this.getColumns();
        const actions = this.getActions();
        const table = UI.createTable({
            columns: columns,
            data: data,
            actions: actions
        });

        container.innerHTML = '';
        container.appendChild(table);
        
        this.currentData = data;
    }
    
    /**
     * 获取空状态HTML
     */
    getEmptyStateHtml() {
        return `
            <div class="empty-state">
                <i class="fas fa-inbox"></i>
                <h3>暂无数据</h3>
                <p>点击"添加"按钮创建第一条记录</p>
            </div>
        `;
    }
    
    /**
     * 处理搜索
     */
    async handleSearch(searchParams) {
        this.searchParams = searchParams;
        try {
            // 显示加载状态
            const container = this.element.querySelector('#tableContainer');
            if (container) {
                container.innerHTML = '<div class="loading"><i class="fas fa-spinner fa-spin"></i> 正在搜索...</div>';
            }

            const data = await this.loadData(searchParams);
            this.displayData(data);
        } catch (error) {
            Utils.log.error('搜索失败:', error);
            if (window.UI && typeof UI.showMessage === 'function') {
                UI.showMessage('搜索失败', 'error');
            } else {
                console.error('搜索失败:', error);
            }
        }
    }
    
    /**
     * 处理添加
     */
    handleAdd() {
        // 子类可以重写此方法
        Utils.log.debug('添加操作');
    }
    
    /**
     * 处理编辑
     */
    handleEdit(item) {
        // 子类可以重写此方法
        Utils.log.debug('编辑操作:', item);
    }
    
    /**
     * 处理删除
     */
    async handleDelete(item) {
        // 子类可以重写此方法
        Utils.log.debug('删除操作:', item);
    }
    
    /**
     * 刷新数据
     */
    async refresh() {
        try {
            // 显示加载状态
            const container = this.element.querySelector('#tableContainer');
            if (container) {
                container.innerHTML = '<div class="loading"><i class="fas fa-spinner fa-spin"></i> 正在刷新...</div>';
            }

            const data = await this.loadData(this.searchParams);
            this.displayData(data);
        } catch (error) {
            Utils.log.error('刷新失败:', error);
            if (window.UI && typeof UI.showMessage === 'function') {
                UI.showMessage('刷新失败', 'error');
            } else {
                console.error('刷新失败:', error);
            }
        }
    }
}

// 导出基类
window.BasePage = BasePage;
window.BaseTablePage = BaseTablePage;
