/**
 * 页面管理器核心类
 * 负责页面的注册、显示、切换等核心功能
 */
class PageManager {
    constructor() {
        this.pages = new Map();
        this.currentPage = null;
        this.init();
    }
    
    /**
     * 初始化页面管理器
     */
    init() {
        Utils.log.debug('页面管理器初始化');
    }
    
    /**
     * 注册页面
     * @param {string} pageId - 页面ID
     * @param {Function} createFunction - 页面创建函数
     */
    registerPage(pageId, createFunction) {
        this.pages.set(pageId, createFunction);
        Utils.log.debug(`注册页面: ${pageId}`);
    }
    
    /**
     * 显示页面
     * @param {string} pageId - 页面ID
     */
    showPage(pageId) {
        // 检查权限（如果Auth模块可用）
        if (window.Auth && typeof Auth.canAccessPage === 'function') {
            if (!Auth.canAccessPage(pageId)) {
                Utils.log.warn(`用户无权限访问页面: ${pageId}`);
                return;
            }
        }
        
        const mainContent = document.getElementById('mainContent');
        if (!mainContent) {
            Utils.log.error('未找到主内容容器');
            return;
        }
        
        // 查找现有页面元素
        let pageElement = document.getElementById(pageId);
        
        if (!pageElement) {
            // 页面不存在，创建新页面
            const createFunction = this.pages.get(pageId);
            if (createFunction) {
                try {
                    const result = createFunction();

                    // 处理异步返回
                    if (result && typeof result.then === 'function') {
                        result.then(element => {
                            if (element && element.nodeType === Node.ELEMENT_NODE) {
                                element.id = pageId;
                                element.className = 'page';
                                mainContent.appendChild(element);
                                this.displayPage(element, pageId);
                            }
                        }).catch(error => {
                            Utils.log.error(`异步创建页面失败: ${pageId}`, error);
                        });
                        return; // 异步处理，直接返回
                    } else if (result && result.nodeType === Node.ELEMENT_NODE) {
                        pageElement = result;
                        pageElement.id = pageId;
                        pageElement.className = 'page';
                        mainContent.appendChild(pageElement);
                    } else {
                        Utils.log.error(`页面创建函数返回了无效的元素: ${pageId}`, result);
                        return;
                    }
                    Utils.log.debug(`创建页面: ${pageId}`);
                } catch (error) {
                    Utils.log.error(`创建页面失败: ${pageId}`, error);
                    UI.showMessage('页面加载失败', 'error');
                    return;
                }
            } else {
                Utils.log.error(`页面不存在: ${pageId}`);
                UI.showMessage('页面不存在', 'error');
                return;
            }
        }
        
        // 隐藏所有页面
        const allPages = mainContent.querySelectorAll('.page');
        allPages.forEach(page => {
            page.classList.remove('active');
        });
        
        this.displayPage(pageElement, pageId);
    }

    /**
     * 显示页面元素
     */
    displayPage(pageElement, pageId) {
        const mainContent = document.getElementById('mainContent');
        if (!mainContent) return;

        // 隐藏所有页面
        const allPages = mainContent.querySelectorAll('.page');
        allPages.forEach(page => {
            page.classList.remove('active');
        });

        // 显示目标页面
        pageElement.classList.add('active');
        this.currentPage = pageId;

        // 更新侧边栏激活状态
        this.updateSidebarActive(pageId);

        Utils.log.debug(`显示页面: ${pageId}`);
    }
    
    /**
     * 更新侧边栏激活状态
     * @param {string} pageId - 当前页面ID
     */
    updateSidebarActive(pageId) {
        const sidebarLinks = document.querySelectorAll('.sidebar a');
        sidebarLinks.forEach(link => {
            link.classList.remove('active');
            if (link.dataset.page === pageId) {
                link.classList.add('active');
            }
        });
    }
    
    /**
     * 获取当前页面ID
     */
    getCurrentPage() {
        return this.currentPage;
    }
    
    /**
     * 刷新当前页面
     */
    refreshCurrentPage() {
        if (this.currentPage) {
            const pageElement = document.getElementById(this.currentPage);
            if (pageElement) {
                pageElement.remove();
                this.showPage(this.currentPage);
            }
        }
    }
}

// 立即创建全局页面管理器实例
const pageManagerInstance = new PageManager();

// 创建全局对象，确保方法正确绑定
window.PageManager = {
    pages: pageManagerInstance.pages,
    currentPage: pageManagerInstance.currentPage,

    registerPage: function(pageId, createFunction) {
        return pageManagerInstance.registerPage(pageId, createFunction);
    },

    showPage: function(pageId) {
        return pageManagerInstance.showPage(pageId);
    },

    updateSidebarActive: function(pageId) {
        return pageManagerInstance.updateSidebarActive(pageId);
    },

    displayPage: function(pageElement, pageId) {
        return pageManagerInstance.displayPage(pageElement, pageId);
    },

    getCurrentPage: function() {
        return pageManagerInstance.getCurrentPage();
    },

    refreshCurrentPage: function() {
        return pageManagerInstance.refreshCurrentPage();
    }
};

// 调试信息
console.log('PageManager initialized:', {
    instance: !!window.PageManager,
    registerPage: typeof window.PageManager.registerPage,
    showPage: typeof window.PageManager.showPage,
    pages: window.PageManager.pages
});
