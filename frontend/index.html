<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>清水河管理系统</title>
    <link rel="stylesheet" href="css/style.css">
    <link rel="stylesheet" href="css/components.css">
    <!-- 引入 Font Awesome 图标库 -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body>
    <!-- 顶部导航栏 -->
    <nav class="navbar">
        <div class="nav-container">
            <div class="nav-logo">
                <i class="fas fa-water"></i>
                <span>清水河管理系统</span>
            </div>
            <div class="nav-menu">
                <div class="nav-item" id="userInfo">
                    <i class="fas fa-user"></i>
                    <span id="currentUser">未登录</span>
                </div>
                <button class="nav-btn" id="logoutBtn" style="display: none;">
                    <i class="fas fa-sign-out-alt"></i>
                    退出登录
                </button>
            </div>
        </div>
    </nav>

    <!-- 主要内容区域 -->
    <div class="main-container">
        <!-- 侧边栏 -->
        <aside class="sidebar" id="sidebar" style="display: none;">
            <div class="sidebar-menu">
                <div class="menu-section">
                    <h3><i class="fas fa-map-marker-alt"></i> 地址解析</h3>
                    <ul>
                        <li><a href="#" data-page="address-parse"><i class="fas fa-search"></i> 地址解析</a></li>
                        <li><a href="#" data-page="address-test"><i class="fas fa-database"></i> 测试数据</a></li>
                    </ul>
                </div>
                <div class="menu-section">
                    <h3><i class="fas fa-users-cog"></i> 系统管理</h3>
                    <ul>
                        <li><a href="#" data-page="user-management"><i class="fas fa-users"></i> 用户管理</a></li>
                        <li><a href="#" data-page="role-management"><i class="fas fa-user-tag"></i> 角色管理</a></li>
                        <li><a href="#" data-page="permission-management"><i class="fas fa-key"></i> 权限管理</a></li>
                    </ul>
                </div>
                <div class="menu-section">
                    <h3><i class="fas fa-cloud-upload-alt"></i> 文件管理</h3>
                    <ul>
                        <li><a href="#" data-page="file-upload"><i class="fas fa-upload"></i> 文件上传</a></li>
                        <li><a href="#" data-page="file-download"><i class="fas fa-download"></i> 文件下载</a></li>
                    </ul>
                </div>
            </div>
        </aside>

        <!-- 主内容区 -->
        <main class="content" id="mainContent">
            <!-- 登录页面 -->
            <div id="loginPage" class="page active">
                <div class="login-container">
                    <div class="login-box">
                        <div class="login-header">
                            <i class="fas fa-water"></i>
                            <h2>清水河管理系统</h2>
                            <p>请登录您的账户</p>
                        </div>
                        <form id="loginForm" class="login-form">
                            <div class="form-group">
                                <label for="username">
                                    <i class="fas fa-user"></i>
                                    用户名
                                </label>
                                <input type="text" id="username" name="username" required placeholder="请输入用户名">
                            </div>
                            <div class="form-group">
                                <label for="password">
                                    <i class="fas fa-lock"></i>
                                    密码
                                </label>
                                <input type="password" id="password" name="password" required placeholder="请输入密码">
                            </div>
                            <button type="submit" class="login-btn">
                                <i class="fas fa-sign-in-alt"></i>
                                登录
                            </button>
                        </form>
                        <div class="login-footer">
                            <p>忘记密码？<a href="#" id="resetPasswordLink">重置密码</a></p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 仪表板页面 -->
            <div id="dashboardPage" class="page">
                <div class="dashboard">
                    <h1>欢迎使用清水河管理系统</h1>
                    <div class="dashboard-cards">
                        <div class="card">
                            <div class="card-icon">
                                <i class="fas fa-map-marker-alt"></i>
                            </div>
                            <div class="card-content">
                                <h3>地址解析</h3>
                                <p>智能解析地址信息，提取省市区详细信息</p>
                                <button class="card-btn" data-page="address-parse">开始使用</button>
                            </div>
                        </div>
                        <div class="card">
                            <div class="card-icon">
                                <i class="fas fa-users"></i>
                            </div>
                            <div class="card-content">
                                <h3>用户管理</h3>
                                <p>管理系统用户，分配角色和权限</p>
                                <button class="card-btn" data-page="user-management">开始使用</button>
                            </div>
                        </div>
                        <div class="card">
                            <div class="card-icon">
                                <i class="fas fa-cloud-upload-alt"></i>
                            </div>
                            <div class="card-content">
                                <h3>文件管理</h3>
                                <p>上传和下载文件，管理系统资源</p>
                                <button class="card-btn" data-page="file-upload">开始使用</button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 其他页面将通过 JavaScript 动态加载 -->
        </main>
    </div>

    <!-- 加载提示 -->
    <div id="loadingOverlay" class="loading-overlay">
        <div class="loading-spinner">
            <i class="fas fa-spinner fa-spin"></i>
            <p>加载中...</p>
        </div>
    </div>

    <!-- 消息提示 -->
    <div id="messageContainer" class="message-container"></div>

    <!-- 引入 JavaScript 文件 -->
    <script src="js/config.js"></script>
    <script src="js/utils.js"></script>
    <script src="js/api.js"></script>
    <script src="js/auth.js"></script>
    <script src="js/components.js"></script>

    <!-- 核心框架 -->
    <script src="js/core/base-page.js"></script>
    <script src="js/core/page-manager.js"></script>
    <script src="js/page-manager-fallback.js"></script>

    <!-- 业务页面 -->
    <script src="js/pages/dashboard/dashboard.js"></script>
    <script src="js/pages/admin/user-management.js"></script>
    <script src="js/pages/admin/role-management.js"></script>
    <script src="js/pages/admin/permission-management.js"></script>
    <script src="js/pages/business/address-parse.js"></script>
    <script src="js/pages/business/file-management.js"></script>

    <script src="js/app.js"></script>
</body>
</html>
