<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>页面测试 - 清水河管理系统</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 50px auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .btn {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        .btn:hover {
            background: #0056b3;
        }
        .result {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 5px;
            padding: 15px;
            margin: 10px 0;
            white-space: pre-wrap;
            font-family: monospace;
        }
        .error {
            background: #f8d7da;
            border-color: #f5c6cb;
            color: #721c24;
        }
        .success {
            background: #d4edda;
            border-color: #c3e6cb;
            color: #155724;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>页面模块测试</h1>
        <p>这个页面用于测试新的模块化页面系统是否正常工作。</p>
        
        <div>
            <button class="btn" onclick="testPageManager()">测试PageManager</button>
            <button class="btn" onclick="testBasePage()">测试BasePage</button>
            <button class="btn" onclick="testAPI()">测试API调用</button>
            <button class="btn" onclick="testUtils()">测试Utils</button>
            <button class="btn" onclick="testAuth()">测试Auth</button>
        </div>
        
        <div id="result" class="result"></div>
    </div>

    <!-- 引入所有必要的JavaScript文件 -->
    <script src="js/config.js"></script>
    <script src="js/utils.js"></script>
    <script src="js/api.js"></script>
    <script src="js/auth.js"></script>
    <script src="js/components.js"></script>
    
    <!-- 核心框架 -->
    <script src="js/core/base-page.js"></script>
    <script src="js/core/page-manager.js"></script>
    
    <!-- 业务页面 -->
    <script src="js/pages/dashboard/dashboard.js"></script>
    <script src="js/pages/admin/user-management.js"></script>
    <script src="js/pages/admin/role-management.js"></script>
    <script src="js/pages/admin/permission-management.js"></script>
    <script src="js/pages/business/address-parse.js"></script>
    <script src="js/pages/business/file-management.js"></script>

    <script>
        function log(message, type = 'info') {
            const resultDiv = document.getElementById('result');
            const timestamp = new Date().toLocaleTimeString();
            const className = type === 'error' ? 'error' : (type === 'success' ? 'success' : '');
            resultDiv.className = `result ${className}`;
            resultDiv.textContent += `[${timestamp}] ${message}\n`;
            resultDiv.scrollTop = resultDiv.scrollHeight;
        }
        
        function testPageManager() {
            log('开始测试PageManager...');
            
            try {
                if (typeof window.PageManager === 'undefined') {
                    log('错误: PageManager未定义', 'error');
                    return;
                }
                
                if (typeof PageManager.registerPage !== 'function') {
                    log('错误: PageManager.registerPage不是函数', 'error');
                    return;
                }
                
                if (typeof PageManager.showPage !== 'function') {
                    log('错误: PageManager.showPage不是函数', 'error');
                    return;
                }
                
                log('PageManager基本功能正常', 'success');
                
                // 测试页面注册数量
                const pageCount = PageManager.pages ? PageManager.pages.size : 0;
                log(`已注册页面数量: ${pageCount}`);
                
                if (pageCount > 0) {
                    log('页面注册成功', 'success');
                } else {
                    log('警告: 没有页面被注册', 'error');
                }
                
            } catch (error) {
                log(`PageManager测试失败: ${error.message}`, 'error');
            }
        }
        
        function testBasePage() {
            log('开始测试BasePage...');
            
            try {
                if (typeof window.BasePage === 'undefined') {
                    log('错误: BasePage未定义', 'error');
                    return;
                }
                
                if (typeof window.BaseTablePage === 'undefined') {
                    log('错误: BaseTablePage未定义', 'error');
                    return;
                }
                
                // 创建一个测试页面实例
                const testPage = new BasePage('test-page', '测试页面');
                
                if (testPage.pageId === 'test-page' && testPage.title === '测试页面') {
                    log('BasePage实例创建成功', 'success');
                } else {
                    log('BasePage实例创建失败', 'error');
                }
                
            } catch (error) {
                log(`BasePage测试失败: ${error.message}`, 'error');
            }
        }
        
        function testAPI() {
            log('开始测试API模块...');
            
            try {
                if (typeof window.API === 'undefined') {
                    log('错误: API未定义', 'error');
                    return;
                }
                
                if (typeof API.get !== 'function') {
                    log('错误: API.get不是函数', 'error');
                    return;
                }
                
                if (typeof API.post !== 'function') {
                    log('错误: API.post不是函数', 'error');
                    return;
                }
                
                log('API模块基本功能正常', 'success');
                
            } catch (error) {
                log(`API测试失败: ${error.message}`, 'error');
            }
        }
        
        function testUtils() {
            log('开始测试Utils模块...');
            
            try {
                if (typeof window.Utils === 'undefined') {
                    log('错误: Utils未定义', 'error');
                    return;
                }
                
                if (typeof Utils.formatDate !== 'function') {
                    log('错误: Utils.formatDate不是函数', 'error');
                    return;
                }
                
                // 测试日期格式化
                const testDate = new Date();
                const formattedDate = Utils.formatDate(testDate);
                log(`日期格式化测试: ${formattedDate}`);
                
                if (formattedDate && formattedDate.length > 0) {
                    log('Utils模块功能正常', 'success');
                } else {
                    log('Utils.formatDate返回空值', 'error');
                }
                
            } catch (error) {
                log(`Utils测试失败: ${error.message}`, 'error');
            }
        }
        
        function testAuth() {
            log('开始测试Auth模块...');
            
            try {
                if (typeof window.Auth === 'undefined') {
                    log('错误: Auth未定义', 'error');
                    return;
                }
                
                if (typeof Auth.isAuthenticated !== 'function') {
                    log('错误: Auth.isAuthenticated不是函数', 'error');
                    return;
                }
                
                if (typeof Auth.canAccessPage !== 'function') {
                    log('错误: Auth.canAccessPage不是函数', 'error');
                    return;
                }
                
                // 测试权限检查
                const canAccessLogin = Auth.canAccessPage('loginPage');
                log(`登录页面访问权限: ${canAccessLogin}`);
                
                const isAuth = Auth.isAuthenticated();
                log(`当前认证状态: ${isAuth}`);
                
                log('Auth模块功能正常', 'success');
                
            } catch (error) {
                log(`Auth测试失败: ${error.message}`, 'error');
            }
        }
        
        // 页面加载时自动运行基本测试
        window.onload = function() {
            log('页面加载完成，开始自动测试...');
            
            setTimeout(() => {
                testPageManager();
                testBasePage();
                testAPI();
                testUtils();
                testAuth();
                
                log('所有测试完成！', 'success');
            }, 1000);
        };
    </script>
</body>
</html>
