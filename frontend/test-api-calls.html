<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>API调用测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1000px;
            margin: 20px auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .btn {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        .btn:hover {
            background: #0056b3;
        }
        .result {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 5px;
            padding: 15px;
            margin: 10px 0;
            white-space: pre-wrap;
            font-family: monospace;
            max-height: 400px;
            overflow-y: auto;
        }
        .error {
            background: #f8d7da;
            border-color: #f5c6cb;
            color: #721c24;
        }
        .success {
            background: #d4edda;
            border-color: #c3e6cb;
            color: #155724;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>API调用测试</h1>
        <p>测试所有API调用是否正常工作</p>
        
        <div>
            <button class="btn" onclick="testUserAPI()">测试用户API</button>
            <button class="btn" onclick="testRoleAPI()">测试角色API</button>
            <button class="btn" onclick="testPermissionAPI()">测试权限API</button>
            <button class="btn" onclick="testAddressAPI()">测试地址API</button>
            <button class="btn" onclick="testFileAPI()">测试文件API</button>
            <button class="btn" onclick="clearLog()">清空日志</button>
        </div>
        
        <div id="result" class="result"></div>
    </div>

    <!-- 引入必要的JavaScript文件 -->
    <script src="js/config.js"></script>
    <script src="js/utils.js"></script>
    <script src="js/api.js"></script>
    <script src="js/auth.js"></script>
    <script src="js/components.js"></script>

    <script>
        function log(message, type = 'info') {
            const resultDiv = document.getElementById('result');
            const timestamp = new Date().toLocaleTimeString();
            const className = type === 'error' ? 'error' : (type === 'success' ? 'success' : '');
            
            const logEntry = `[${timestamp}] ${message}\n`;
            resultDiv.textContent += logEntry;
            resultDiv.className = `result ${className}`;
            resultDiv.scrollTop = resultDiv.scrollHeight;
            
            console.log(`[API Test] ${message}`);
        }
        
        function clearLog() {
            document.getElementById('result').textContent = '';
            document.getElementById('result').className = 'result';
        }
        
        async function testUserAPI() {
            log('=== 测试用户API ===');
            
            try {
                // 检查API对象
                if (typeof window.API === 'undefined') {
                    log('错误: API对象未定义', 'error');
                    return;
                }
                
                if (typeof API.post !== 'function') {
                    log('错误: API.post方法不存在', 'error');
                    return;
                }
                
                log('API对象检查通过');
                
                // 测试用户查询API
                const queryData = {
                    current: 1,
                    size: 5,
                    paramObj: {}
                };
                
                log('正在调用用户查询API...');
                const result = await API.post(CONFIG.API_ENDPOINTS.USER.QUERY, queryData);
                
                if (result && result.code === 200) {
                    log(`用户查询成功，返回 ${result.records?.length || 0} 条记录`, 'success');
                    log(`总记录数: ${result.total || 0}`);
                } else {
                    log(`用户查询失败: ${result?.message || '未知错误'}`, 'error');
                }
                
            } catch (error) {
                log(`用户API测试失败: ${error.message}`, 'error');
            }
        }
        
        async function testRoleAPI() {
            log('=== 测试角色API ===');
            
            try {
                const queryData = {
                    current: 1,
                    size: 5,
                    paramObj: {}
                };
                
                log('正在调用角色查询API...');
                const result = await API.post(CONFIG.API_ENDPOINTS.ROLE.QUERY, queryData);
                
                if (result && result.code === 200) {
                    log(`角色查询成功，返回 ${result.records?.length || 0} 条记录`, 'success');
                    log(`总记录数: ${result.total || 0}`);
                } else {
                    log(`角色查询失败: ${result?.message || '未知错误'}`, 'error');
                }
                
            } catch (error) {
                log(`角色API测试失败: ${error.message}`, 'error');
            }
        }
        
        async function testPermissionAPI() {
            log('=== 测试权限API ===');
            
            try {
                const queryData = {
                    current: 1,
                    size: 5,
                    paramObj: {}
                };
                
                log('正在调用权限查询API...');
                const result = await API.post(CONFIG.API_ENDPOINTS.PERMISSION.QUERY, queryData);
                
                if (result && result.code === 200) {
                    log(`权限查询成功，返回 ${result.records?.length || 0} 条记录`, 'success');
                    log(`总记录数: ${result.total || 0}`);
                } else {
                    log(`权限查询失败: ${result?.message || '未知错误'}`, 'error');
                }
                
            } catch (error) {
                log(`权限API测试失败: ${error.message}`, 'error');
            }
        }
        
        async function testAddressAPI() {
            log('=== 测试地址API ===');
            
            try {
                // 检查API.get方法
                if (typeof API.get !== 'function') {
                    log('错误: API.get方法不存在', 'error');
                    return;
                }
                
                log('正在调用地址测试数据API...');
                const result = await API.get(CONFIG.API_ENDPOINTS.ADDRESS.TEST_DATA);
                
                if (result && result.code === 200) {
                    log(`地址测试数据获取成功，返回 ${result.obj?.length || 0} 条记录`, 'success');
                } else {
                    log(`地址测试数据获取失败: ${result?.message || '未知错误'}`, 'error');
                }
                
            } catch (error) {
                log(`地址API测试失败: ${error.message}`, 'error');
            }
        }
        
        async function testFileAPI() {
            log('=== 测试文件API ===');
            
            try {
                // 检查API.upload方法
                if (typeof API.upload !== 'function') {
                    log('错误: API.upload方法不存在', 'error');
                    return;
                }
                
                log('API.upload方法存在', 'success');
                log('文件上传功能需要实际文件才能测试');
                
            } catch (error) {
                log(`文件API测试失败: ${error.message}`, 'error');
            }
        }
        
        // 页面加载时自动运行基本测试
        window.onload = function() {
            log('页面加载完成，开始API测试...');
            
            // 检查基本对象
            log(`CONFIG对象: ${typeof window.CONFIG}`);
            log(`API对象: ${typeof window.API}`);
            log(`Utils对象: ${typeof window.Utils}`);
            log(`Auth对象: ${typeof window.Auth}`);
            log(`UI对象: ${typeof window.UI}`);
            
            if (typeof window.CONFIG === 'undefined') {
                log('警告: CONFIG对象未定义', 'error');
            }
            
            if (typeof window.API === 'undefined') {
                log('警告: API对象未定义', 'error');
            }
            
            log('基本对象检查完成');
        };
    </script>
</body>
</html>
