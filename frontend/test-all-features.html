<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>功能测试 - 清水河管理系统</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 20px auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .btn {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        .btn:hover {
            background: #0056b3;
        }
        .btn-danger {
            background: #dc3545;
        }
        .btn-danger:hover {
            background: #c82333;
        }
        .result {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 5px;
            padding: 15px;
            margin: 10px 0;
            white-space: pre-wrap;
            font-family: monospace;
            max-height: 400px;
            overflow-y: auto;
        }
        .error {
            background: #f8d7da;
            border-color: #f5c6cb;
            color: #721c24;
        }
        .success {
            background: #d4edda;
            border-color: #c3e6cb;
            color: #155724;
        }
        .test-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 8px;
        }
        .test-section h3 {
            margin-top: 0;
            color: #333;
        }
        .form-group {
            margin-bottom: 15px;
        }
        .form-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: 500;
        }
        .form-group input, .form-group textarea {
            width: 100%;
            padding: 8px 12px;
            border: 1px solid #ddd;
            border-radius: 4px;
            box-sizing: border-box;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>功能测试 - 清水河管理系统</h1>
        <p>测试所有新增和修复的功能</p>
        
        <div class="test-section">
            <h3>1. 删除功能测试</h3>
            <button class="btn" onclick="testUserDelete()">测试用户删除</button>
            <button class="btn" onclick="testRoleDelete()">测试角色删除</button>
            <button class="btn" onclick="testPermissionDelete()">测试权限删除</button>
        </div>
        
        <div class="test-section">
            <h3>2. 地址解析功能测试</h3>
            <div class="form-group">
                <label for="addressTestInput">输入地址信息：</label>
                <textarea id="addressTestInput" rows="4" 
                    placeholder="北京市朝阳区建国门外大街1号&#10;上海市浦东新区陆家嘴环路1000号&#10;广州市天河区珠江新城">北京市朝阳区建国门外大街1号
上海市浦东新区陆家嘴环路1000号
广州市天河区珠江新城
深圳市南山区科技园</textarea>
            </div>
            <button class="btn" onclick="testAddressParse()">测试地址解析</button>
        </div>
        
        <div class="test-section">
            <h3>3. 数据查询测试</h3>
            <button class="btn" onclick="testUserQuery()">测试用户查询</button>
            <button class="btn" onclick="testRoleQuery()">测试角色查询</button>
            <button class="btn" onclick="testPermissionQuery()">测试权限查询</button>
        </div>
        
        <button class="btn btn-danger" onclick="clearLog()">清空日志</button>
        
        <div id="result" class="result"></div>
    </div>

    <!-- 引入必要的JavaScript文件 -->
    <script src="js/config.js"></script>
    <script src="js/utils.js"></script>
    <script src="js/api.js"></script>
    <script src="js/auth.js"></script>
    <script src="js/components.js"></script>

    <script>
        function log(message, type = 'info') {
            const resultDiv = document.getElementById('result');
            const timestamp = new Date().toLocaleTimeString();
            const className = type === 'error' ? 'error' : (type === 'success' ? 'success' : '');
            
            const logEntry = `[${timestamp}] ${message}\n`;
            resultDiv.textContent += logEntry;
            resultDiv.className = `result ${className}`;
            resultDiv.scrollTop = resultDiv.scrollHeight;
            
            console.log(`[Feature Test] ${message}`);
        }
        
        function clearLog() {
            document.getElementById('result').textContent = '';
            document.getElementById('result').className = 'result';
        }
        
        // 删除功能测试
        async function testUserDelete() {
            log('=== 测试用户删除功能 ===');
            
            try {
                // 首先查询用户列表
                const queryResult = await API.post(CONFIG.API_ENDPOINTS.USER.QUERY, {
                    current: 1,
                    size: 5,
                    paramObj: {}
                });
                
                if (queryResult && queryResult.records && queryResult.records.length > 0) {
                    const user = queryResult.records[0];
                    log(`找到测试用户: ${user.username} (ID: ${user.id})`);
                    
                    // 注意：这里只是测试API调用，不会真正删除
                    log('模拟删除操作（不会真正删除）');
                    log(`删除API端点: ${CONFIG.API_ENDPOINTS.USER.DELETE_BY_ID}`);
                    log('删除功能API配置正确', 'success');
                } else {
                    log('没有找到可测试的用户', 'error');
                }
            } catch (error) {
                log(`用户删除测试失败: ${error.message}`, 'error');
            }
        }
        
        async function testRoleDelete() {
            log('=== 测试角色删除功能 ===');
            
            try {
                const queryResult = await API.post(CONFIG.API_ENDPOINTS.ROLE.QUERY, {
                    current: 1,
                    size: 5,
                    paramObj: {}
                });
                
                if (queryResult && queryResult.records && queryResult.records.length > 0) {
                    const role = queryResult.records[0];
                    log(`找到测试角色: ${role.name} (ID: ${role.id})`);
                    log(`删除API端点: ${CONFIG.API_ENDPOINTS.ROLE.DELETE_BY_ID}`);
                    log('角色删除功能API配置正确', 'success');
                } else {
                    log('没有找到可测试的角色', 'error');
                }
            } catch (error) {
                log(`角色删除测试失败: ${error.message}`, 'error');
            }
        }
        
        async function testPermissionDelete() {
            log('=== 测试权限删除功能 ===');
            
            try {
                const queryResult = await API.post(CONFIG.API_ENDPOINTS.PERMISSION.QUERY, {
                    current: 1,
                    size: 5,
                    paramObj: {}
                });
                
                if (queryResult && queryResult.records && queryResult.records.length > 0) {
                    const permission = queryResult.records[0];
                    log(`找到测试权限: ${permission.name} (ID: ${permission.id})`);
                    log(`删除API端点: ${CONFIG.API_ENDPOINTS.PERMISSION.DELETE_BY_ID}`);
                    log('权限删除功能API配置正确', 'success');
                } else {
                    log('没有找到可测试的权限', 'error');
                }
            } catch (error) {
                log(`权限删除测试失败: ${error.message}`, 'error');
            }
        }
        
        // 地址解析测试
        async function testAddressParse() {
            log('=== 测试地址解析功能 ===');
            
            try {
                const input = document.getElementById('addressTestInput').value.trim();
                
                if (!input) {
                    log('请输入地址信息', 'error');
                    return;
                }
                
                const addresses = input.split('\n').filter(line => line.trim());
                log(`准备解析 ${addresses.length} 条地址`);
                
                const result = await API.post(CONFIG.API_ENDPOINTS.ADDRESS.PARSE, addresses);
                
                if (result && result.code === 200) {
                    log('地址解析成功！', 'success');
                    log(`解析结果: ${JSON.stringify(result.obj, null, 2)}`);
                    
                    result.obj.forEach((item, index) => {
                        log(`地址${index + 1}: ${item.address}`);
                        log(`  识别省份: ${item.medicareProv || '未识别'}`);
                        log(`  识别城市: ${item.medicareCity || '未识别'}`);
                    });
                } else {
                    log(`地址解析失败: ${result?.message || '未知错误'}`, 'error');
                }
            } catch (error) {
                log(`地址解析测试失败: ${error.message}`, 'error');
            }
        }
        
        // 查询功能测试
        async function testUserQuery() {
            log('=== 测试用户查询功能 ===');
            
            try {
                const result = await API.post(CONFIG.API_ENDPOINTS.USER.QUERY, {
                    current: 1,
                    size: 3,
                    paramObj: {}
                });
                
                if (result && result.records) {
                    log(`用户查询成功，共 ${result.total} 条记录`, 'success');
                    result.records.forEach(user => {
                        log(`用户: ${user.username} (ID: ${user.id})`);
                    });
                } else {
                    log('用户查询失败', 'error');
                }
            } catch (error) {
                log(`用户查询测试失败: ${error.message}`, 'error');
            }
        }
        
        async function testRoleQuery() {
            log('=== 测试角色查询功能 ===');
            
            try {
                const result = await API.post(CONFIG.API_ENDPOINTS.ROLE.QUERY, {
                    current: 1,
                    size: 3,
                    paramObj: {}
                });
                
                if (result && result.records) {
                    log(`角色查询成功，共 ${result.total} 条记录`, 'success');
                    result.records.forEach(role => {
                        log(`角色: ${role.name} (ID: ${role.id})`);
                    });
                } else {
                    log('角色查询失败', 'error');
                }
            } catch (error) {
                log(`角色查询测试失败: ${error.message}`, 'error');
            }
        }
        
        async function testPermissionQuery() {
            log('=== 测试权限查询功能 ===');
            
            try {
                const result = await API.post(CONFIG.API_ENDPOINTS.PERMISSION.QUERY, {
                    current: 1,
                    size: 3,
                    paramObj: {}
                });
                
                if (result && result.records) {
                    log(`权限查询成功，共 ${result.total} 条记录`, 'success');
                    result.records.forEach(permission => {
                        log(`权限: ${permission.name} (ID: ${permission.id})`);
                    });
                } else {
                    log('权限查询失败', 'error');
                }
            } catch (error) {
                log(`权限查询测试失败: ${error.message}`, 'error');
            }
        }
        
        // 页面加载时自动运行基本测试
        window.onload = function() {
            log('功能测试页面加载完成');
            log('请点击相应按钮测试各项功能');
        };
    </script>
</body>
</html>
