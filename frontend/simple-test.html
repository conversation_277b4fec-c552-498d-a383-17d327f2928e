<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>简化测试 - 清水河管理系统</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <link rel="stylesheet" href="css/style.css">
    <link rel="stylesheet" href="css/components.css">
</head>
<body>
    <div class="app-container">
        <!-- 侧边栏 -->
        <div class="sidebar">
            <div class="sidebar-header">
                <h2><i class="fas fa-water"></i> 清水河</h2>
            </div>
            <nav class="sidebar-nav">
                <a href="#" data-page="dashboard" class="active">
                    <i class="fas fa-tachometer-alt"></i>
                    仪表板
                </a>
                <a href="#" data-page="user-management">
                    <i class="fas fa-users"></i>
                    用户管理
                </a>
                <a href="#" data-page="role-management">
                    <i class="fas fa-user-tag"></i>
                    角色管理
                </a>
            </nav>
        </div>

        <!-- 主内容区 -->
        <div class="main-content">
            <!-- 顶部栏 -->
            <div class="topbar">
                <div class="topbar-left">
                    <h1 id="pageTitle">仪表板</h1>
                </div>
                <div class="topbar-right">
                    <span>欢迎，<span id="currentUser">qingshuihe</span></span>
                    <button class="btn btn-secondary" onclick="logout()">
                        <i class="fas fa-sign-out-alt"></i>
                        退出
                    </button>
                </div>
            </div>

            <!-- 页面内容 -->
            <div id="mainContent">
                <!-- 默认仪表板 -->
                <div id="dashboard" class="page active">
                    <div class="dashboard-container">
                        <div class="dashboard-header">
                            <h3 class="dashboard-title">
                                <i class="fas fa-tachometer-alt"></i>
                                系统仪表板
                            </h3>
                            <p class="dashboard-description">
                                欢迎使用清水河管理系统
                            </p>
                        </div>
                        
                        <div class="stats-grid">
                            <div class="stat-card">
                                <div class="stat-icon">
                                    <i class="fas fa-users"></i>
                                </div>
                                <div class="stat-content">
                                    <div class="stat-number">0</div>
                                    <div class="stat-label">用户总数</div>
                                </div>
                            </div>
                            
                            <div class="stat-card">
                                <div class="stat-icon">
                                    <i class="fas fa-user-tag"></i>
                                </div>
                                <div class="stat-content">
                                    <div class="stat-number">0</div>
                                    <div class="stat-label">角色数量</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 用户管理页面 -->
                <div id="user-management" class="page">
                    <div class="table-container">
                        <div class="table-header">
                            <h3 class="table-title">
                                <i class="fas fa-users"></i>
                                用户管理
                            </h3>
                            <div class="table-actions">
                                <button class="btn btn-primary">
                                    <i class="fas fa-plus"></i>
                                    添加用户
                                </button>
                            </div>
                        </div>
                        <div class="loading">
                            <i class="fas fa-spinner fa-spin"></i>
                            正在加载用户数据...
                        </div>
                    </div>
                </div>

                <!-- 角色管理页面 -->
                <div id="role-management" class="page">
                    <div class="table-container">
                        <div class="table-header">
                            <h3 class="table-title">
                                <i class="fas fa-user-tag"></i>
                                角色管理
                            </h3>
                            <div class="table-actions">
                                <button class="btn btn-primary">
                                    <i class="fas fa-plus"></i>
                                    添加角色
                                </button>
                            </div>
                        </div>
                        <div class="loading">
                            <i class="fas fa-spinner fa-spin"></i>
                            正在加载角色数据...
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 简化的页面管理
        function showPage(pageId) {
            console.log('显示页面:', pageId);
            
            // 隐藏所有页面
            const pages = document.querySelectorAll('.page');
            pages.forEach(page => {
                page.classList.remove('active');
            });
            
            // 显示目标页面
            const targetPage = document.getElementById(pageId);
            if (targetPage) {
                targetPage.classList.add('active');
                
                // 更新页面标题
                const titles = {
                    'dashboard': '仪表板',
                    'user-management': '用户管理',
                    'role-management': '角色管理'
                };
                document.getElementById('pageTitle').textContent = titles[pageId] || '未知页面';
                
                // 更新侧边栏激活状态
                const links = document.querySelectorAll('.sidebar a');
                links.forEach(link => {
                    link.classList.remove('active');
                    if (link.dataset.page === pageId) {
                        link.classList.add('active');
                    }
                });
            } else {
                console.error('页面不存在:', pageId);
            }
        }
        
        // 绑定导航事件
        document.addEventListener('DOMContentLoaded', function() {
            const links = document.querySelectorAll('.sidebar a[data-page]');
            links.forEach(link => {
                link.addEventListener('click', function(e) {
                    e.preventDefault();
                    const pageId = this.dataset.page;
                    showPage(pageId);
                });
            });
            
            console.log('简化版本已加载，导航功能正常');
        });
        
        function logout() {
            alert('退出功能');
        }
    </script>
</body>
</html>
