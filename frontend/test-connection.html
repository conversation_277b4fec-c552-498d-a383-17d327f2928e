<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>连接测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 50px auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .btn {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        .btn:hover {
            background: #0056b3;
        }
        .result {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 5px;
            padding: 15px;
            margin: 10px 0;
            white-space: pre-wrap;
            font-family: monospace;
        }
        .error {
            background: #f8d7da;
            border-color: #f5c6cb;
            color: #721c24;
        }
        .success {
            background: #d4edda;
            border-color: #c3e6cb;
            color: #155724;
        }
        .warning {
            background: #fff3cd;
            border-color: #ffeaa7;
            color: #856404;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>后端连接测试</h1>
        <p>测试前端是否能正常连接到后端服务</p>
        
        <div>
            <button class="btn" onclick="testConnection()">测试连接</button>
            <button class="btn" onclick="testLogin()">测试登录</button>
            <button class="btn" onclick="testCORS()">测试CORS</button>
            <button class="btn" onclick="clearLog()">清空日志</button>
        </div>
        
        <div id="result" class="result"></div>
    </div>

    <script>
        const API_BASE_URL = 'http://localhost:8080';
        
        function log(message, type = 'info') {
            const resultDiv = document.getElementById('result');
            const timestamp = new Date().toLocaleTimeString();
            const className = type === 'error' ? 'error' : (type === 'success' ? 'success' : (type === 'warning' ? 'warning' : ''));
            
            const logEntry = `[${timestamp}] ${message}\n`;
            resultDiv.textContent += logEntry;
            resultDiv.className = `result ${className}`;
            resultDiv.scrollTop = resultDiv.scrollHeight;
            
            console.log(`[Connection Test] ${message}`);
        }
        
        function clearLog() {
            document.getElementById('result').textContent = '';
            document.getElementById('result').className = 'result';
        }
        
        async function testConnection() {
            log('=== 测试基本连接 ===');
            
            try {
                log('正在测试后端服务连接...');
                
                const response = await fetch(`${API_BASE_URL}/admin/login`, {
                    method: 'OPTIONS',
                    headers: {
                        'Content-Type': 'application/json',
                        'Accept': 'application/json'
                    }
                });
                
                log(`OPTIONS请求状态: ${response.status}`, response.ok ? 'success' : 'warning');
                log(`响应头: ${JSON.stringify(Object.fromEntries(response.headers.entries()))}`);
                
                if (response.ok || response.status === 200 || response.status === 204) {
                    log('后端服务连接正常', 'success');
                } else {
                    log(`连接异常，状态码: ${response.status}`, 'warning');
                }
                
            } catch (error) {
                log(`连接失败: ${error.message}`, 'error');
                log('可能的原因:');
                log('1. 后端服务未启动');
                log('2. 端口8081被占用或不正确');
                log('3. 防火墙阻止连接');
                log('4. CORS配置问题');
            }
        }
        
        async function testLogin() {
            log('=== 测试登录接口 ===');
            
            try {
                log('正在测试登录接口...');
                
                const loginData = {
                    username: 'qingshuihe',
                    password: '123456'
                };
                
                const response = await fetch(`${API_BASE_URL}/admin/login`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Accept': 'application/json'
                    },
                    body: JSON.stringify(loginData)
                });
                
                log(`登录请求状态: ${response.status}`);
                
                if (response.ok) {
                    const data = await response.json();
                    log(`登录响应: ${JSON.stringify(data, null, 2)}`, 'success');
                    
                    if (data.code === 200) {
                        log('登录成功！', 'success');
                    } else {
                        log(`登录失败: ${data.message}`, 'error');
                    }
                } else {
                    const errorText = await response.text();
                    log(`登录请求失败: ${response.status} - ${errorText}`, 'error');
                }
                
            } catch (error) {
                log(`登录测试失败: ${error.message}`, 'error');
            }
        }
        
        async function testCORS() {
            log('=== 测试CORS配置 ===');
            
            try {
                log('正在测试CORS预检请求...');
                
                const response = await fetch(`${API_BASE_URL}/admin/login`, {
                    method: 'OPTIONS',
                    headers: {
                        'Origin': window.location.origin,
                        'Access-Control-Request-Method': 'POST',
                        'Access-Control-Request-Headers': 'Content-Type'
                    }
                });
                
                log(`CORS预检状态: ${response.status}`);
                
                const corsHeaders = {
                    'Access-Control-Allow-Origin': response.headers.get('Access-Control-Allow-Origin'),
                    'Access-Control-Allow-Methods': response.headers.get('Access-Control-Allow-Methods'),
                    'Access-Control-Allow-Headers': response.headers.get('Access-Control-Allow-Headers'),
                    'Access-Control-Allow-Credentials': response.headers.get('Access-Control-Allow-Credentials')
                };
                
                log(`CORS响应头: ${JSON.stringify(corsHeaders, null, 2)}`);
                
                if (corsHeaders['Access-Control-Allow-Origin']) {
                    log('CORS配置正常', 'success');
                } else {
                    log('CORS配置可能有问题', 'warning');
                }
                
            } catch (error) {
                log(`CORS测试失败: ${error.message}`, 'error');
            }
        }
        
        // 页面加载时自动测试
        window.onload = function() {
            log('页面加载完成，开始连接测试...');
            log(`测试目标: ${API_BASE_URL}`);
            log(`当前页面: ${window.location.origin}`);
            
            // 自动运行基本连接测试
            setTimeout(testConnection, 1000);
        };
    </script>
</body>
</html>
