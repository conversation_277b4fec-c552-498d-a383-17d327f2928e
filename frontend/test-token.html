<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Token测试页面</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 50px auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .btn {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        .btn:hover {
            background: #0056b3;
        }
        .result {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 5px;
            padding: 15px;
            margin: 10px 0;
            white-space: pre-wrap;
            font-family: monospace;
        }
        .error {
            background: #f8d7da;
            border-color: #f5c6cb;
            color: #721c24;
        }
        .success {
            background: #d4edda;
            border-color: #c3e6cb;
            color: #155724;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Token测试页面</h1>
        <p>这个页面用于测试token的存储和传递是否正确。</p>
        
        <div>
            <button class="btn" onclick="checkToken()">检查当前Token</button>
            <button class="btn" onclick="clearToken()">清除Token</button>
            <button class="btn" onclick="testLogin()">测试登录</button>
            <button class="btn" onclick="testAPI()">测试API调用</button>
        </div>
        
        <div id="result" class="result"></div>
    </div>

    <script>
        const API_BASE_URL = 'http://localhost:8080';
        const TOKEN_KEY = 'qsh_token';
        
        function log(message) {
            const resultDiv = document.getElementById('result');
            const timestamp = new Date().toLocaleTimeString();
            resultDiv.textContent += `[${timestamp}] ${message}\n`;
            resultDiv.scrollTop = resultDiv.scrollHeight;
        }
        
        function checkToken() {
            const token = localStorage.getItem(TOKEN_KEY);
            if (token) {
                log(`当前Token: ${token.substring(0, 50)}...`);
                log(`Token类型: ${typeof token}`);
                log(`Token长度: ${token.length}`);
            } else {
                log('未找到Token');
            }
        }
        
        function clearToken() {
            localStorage.removeItem(TOKEN_KEY);
            log('Token已清除');
        }
        
        async function testLogin() {
            try {
                log('开始测试登录...');
                
                const response = await fetch(`${API_BASE_URL}/admin/login`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        username: 'qingshuihe',
                        pwd: 'qingshuihe'
                    })
                });
                
                const result = await response.json();
                log(`登录响应: ${JSON.stringify(result, null, 2)}`);
                
                if (result.code === 200) {
                    const token = result.data;
                    localStorage.setItem(TOKEN_KEY, token);
                    log(`Token已保存: ${token.substring(0, 50)}...`);
                } else {
                    log(`登录失败: ${result.message}`);
                }
                
            } catch (error) {
                log(`登录错误: ${error.message}`);
            }
        }
        
        async function testAPI() {
            try {
                log('开始测试API调用...');
                
                const token = localStorage.getItem(TOKEN_KEY);
                if (!token) {
                    log('错误: 未找到Token，请先登录');
                    return;
                }
                
                log(`使用Token: ${token.substring(0, 50)}...`);
                
                const headers = {
                    'Content-Type': 'application/json',
                    'Accept': 'application/json',
                    'token': token
                };
                
                log(`请求头: ${JSON.stringify(headers, null, 2)}`);
                
                const response = await fetch(`${API_BASE_URL}/api/address/test-data`, {
                    method: 'GET',
                    headers: headers
                });
                
                log(`响应状态: ${response.status} ${response.statusText}`);
                
                const result = await response.json();
                log(`API响应: ${JSON.stringify(result, null, 2)}`);
                
            } catch (error) {
                log(`API调用错误: ${error.message}`);
            }
        }
        
        // 页面加载时检查token
        window.onload = function() {
            log('页面已加载');
            checkToken();
        };
    </script>
</body>
</html>
