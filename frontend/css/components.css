/* 组件样式文件 */

/* 通用按钮样式 */
.btn {
    padding: 10px 20px;
    border: none;
    border-radius: 8px;
    cursor: pointer;
    font-size: 0.9rem;
    font-weight: 500;
    transition: all 0.3s ease;
    display: inline-flex;
    align-items: center;
    gap: 8px;
    text-decoration: none;
    text-align: center;
    justify-content: center;
}

.btn-primary {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
}

.btn-secondary {
    background: #6c757d;
    color: white;
}

.btn-secondary:hover {
    background: #5a6268;
    transform: translateY(-2px);
}

.btn-success {
    background: #28a745;
    color: white;
}

.btn-success:hover {
    background: #218838;
    transform: translateY(-2px);
}

.btn-danger {
    background: #dc3545;
    color: white;
}

.btn-danger:hover {
    background: #c82333;
    transform: translateY(-2px);
}

.btn-warning {
    background: #ffc107;
    color: #212529;
}

.btn-warning:hover {
    background: #e0a800;
    transform: translateY(-2px);
}

.btn-info {
    background: #17a2b8;
    color: white;
}

.btn-info:hover {
    background: #138496;
    transform: translateY(-2px);
}

.btn-sm {
    padding: 6px 12px;
    font-size: 0.8rem;
}

.btn-lg {
    padding: 15px 30px;
    font-size: 1.1rem;
}

/* 表单样式 */
.form-container {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border-radius: 15px;
    padding: 30px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    margin-bottom: 20px;
}

.form-title {
    color: #333;
    margin-bottom: 25px;
    font-size: 1.5rem;
    display: flex;
    align-items: center;
    gap: 10px;
}

.form-row {
    display: flex;
    gap: 20px;
    margin-bottom: 20px;
}

.form-col {
    flex: 1;
}

.form-group {
    margin-bottom: 20px;
}

.form-label {
    display: block;
    margin-bottom: 8px;
    color: #555;
    font-weight: 500;
}

.form-control {
    width: 100%;
    padding: 12px 15px;
    border: 2px solid #e1e5e9;
    border-radius: 8px;
    font-size: 1rem;
    transition: all 0.3s ease;
    background: rgba(255, 255, 255, 0.8);
}

.form-control:focus {
    outline: none;
    border-color: #667eea;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.form-control.error {
    border-color: #dc3545;
}

.form-control.success {
    border-color: #28a745;
}

.form-text {
    font-size: 0.8rem;
    color: #6c757d;
    margin-top: 5px;
}

.form-text.error {
    color: #dc3545;
}

/* 表格样式 */
.table-container {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border-radius: 15px;
    padding: 20px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    overflow-x: auto;
}

.table-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    flex-wrap: wrap;
    gap: 15px;
}

.table-title {
    color: #333;
    font-size: 1.3rem;
    display: flex;
    align-items: center;
    gap: 10px;
}

.table-actions {
    display: flex;
    gap: 10px;
    flex-wrap: wrap;
}

.data-table {
    width: 100%;
    border-collapse: collapse;
    margin-top: 10px;
}

.data-table th,
.data-table td {
    padding: 12px 15px;
    text-align: left;
    border-bottom: 1px solid #e1e5e9;
}

.data-table th {
    background: #f8f9fa;
    color: #495057;
    font-weight: 600;
    position: sticky;
    top: 0;
    z-index: 10;
}

.data-table tr:hover {
    background: #f8f9ff;
}

.data-table .actions {
    display: flex;
    gap: 8px;
    justify-content: center;
}

/* 搜索框样式 */
.search-container {
    position: relative;
    max-width: 300px;
}

.search-input {
    width: 100%;
    padding: 10px 15px 10px 40px;
    border: 2px solid #e1e5e9;
    border-radius: 25px;
    font-size: 0.9rem;
    transition: all 0.3s ease;
}

.search-input:focus {
    outline: none;
    border-color: #667eea;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.search-icon {
    position: absolute;
    left: 15px;
    top: 50%;
    transform: translateY(-50%);
    color: #6c757d;
}

/* 分页样式 */
.pagination {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 10px;
    margin-top: 20px;
    flex-wrap: wrap;
}

.pagination-info {
    color: #6c757d;
    font-size: 0.9rem;
    margin: 0 15px;
}

.pagination-btn {
    padding: 8px 12px;
    border: 1px solid #dee2e6;
    background: white;
    color: #495057;
    border-radius: 5px;
    cursor: pointer;
    transition: all 0.3s ease;
}

.pagination-btn:hover:not(:disabled) {
    background: #667eea;
    color: white;
    border-color: #667eea;
}

.pagination-btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

.pagination-btn.active {
    background: #667eea;
    color: white;
    border-color: #667eea;
}

/* 模态框样式 */
.modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.5);
    display: none;
    justify-content: center;
    align-items: center;
    z-index: 1000;
    padding: 20px;
}

.modal-overlay.show {
    display: flex;
}

.modal {
    background: white;
    border-radius: 15px;
    max-width: 600px;
    width: 100%;
    max-height: 90vh;
    overflow-y: auto;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
    animation: modalSlideIn 0.3s ease;
}

@keyframes modalSlideIn {
    from { transform: scale(0.8); opacity: 0; }
    to { transform: scale(1); opacity: 1; }
}

.modal-header {
    padding: 20px 30px;
    border-bottom: 1px solid #e1e5e9;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.modal-title {
    color: #333;
    font-size: 1.3rem;
    display: flex;
    align-items: center;
    gap: 10px;
}

.modal-close {
    background: none;
    border: none;
    font-size: 1.5rem;
    color: #6c757d;
    cursor: pointer;
    padding: 5px;
    border-radius: 50%;
    transition: all 0.3s ease;
}

.modal-close:hover {
    background: #f8f9fa;
    color: #495057;
}

.modal-body {
    padding: 30px;
}

.modal-footer {
    padding: 20px 30px;
    border-top: 1px solid #e1e5e9;
    display: flex;
    justify-content: flex-end;
    gap: 10px;
}

/* 标签样式 */
.badge {
    display: inline-block;
    padding: 4px 8px;
    font-size: 0.75rem;
    font-weight: 600;
    border-radius: 12px;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.badge-primary {
    background: #667eea;
    color: white;
}

.badge-success {
    background: #28a745;
    color: white;
}

.badge-danger {
    background: #dc3545;
    color: white;
}

.badge-warning {
    background: #ffc107;
    color: #212529;
}

.badge-info {
    background: #17a2b8;
    color: white;
}

.badge-secondary {
    background: #6c757d;
    color: white;
}

/* 文件上传样式 */
.file-upload-area {
    border: 2px dashed #667eea;
    border-radius: 15px;
    padding: 40px 20px;
    text-align: center;
    background: rgba(102, 126, 234, 0.05);
    transition: all 0.3s ease;
    cursor: pointer;
}

.file-upload-area:hover {
    border-color: #5a6fd8;
    background: rgba(102, 126, 234, 0.1);
}

.file-upload-area.dragover {
    border-color: #5a6fd8;
    background: rgba(102, 126, 234, 0.15);
    transform: scale(1.02);
}

.file-upload-icon {
    font-size: 3rem;
    color: #667eea;
    margin-bottom: 15px;
}

.file-upload-text {
    color: #495057;
    margin-bottom: 10px;
}

.file-upload-hint {
    color: #6c757d;
    font-size: 0.9rem;
}

.file-list {
    margin-top: 20px;
}

.file-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 10px 15px;
    background: #f8f9fa;
    border-radius: 8px;
    margin-bottom: 10px;
}

.file-info {
    display: flex;
    align-items: center;
    gap: 10px;
}

.file-name {
    font-weight: 500;
    color: #495057;
}

.file-size {
    color: #6c757d;
    font-size: 0.9rem;
}

/* 进度条样式 */
.progress {
    width: 100%;
    height: 8px;
    background: #e9ecef;
    border-radius: 4px;
    overflow: hidden;
    margin: 10px 0;
}

.progress-bar {
    height: 100%;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    transition: width 0.3s ease;
    border-radius: 4px;
}

/* 角色和权限管理样式 */
.role-user-assignment,
.permission-role-assignment {
    padding: 20px;
}

.role-user-assignment h4,
.permission-role-assignment h4 {
    margin-bottom: 20px;
    color: #333;
    border-bottom: 2px solid #007bff;
    padding-bottom: 10px;
}

.user-selection,
.role-selection {
    margin-top: 20px;
}

.search-users {
    display: flex;
    gap: 10px;
    margin-bottom: 20px;
}

.search-users input {
    flex: 1;
    padding: 8px 12px;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 14px;
}

.user-list,
.role-list {
    max-height: 400px;
    overflow-y: auto;
    border: 1px solid #ddd;
    border-radius: 8px;
    background: #fff;
}

.user-item,
.role-item {
    padding: 12px 15px;
    border-bottom: 1px solid #eee;
    transition: background-color 0.2s ease;
}

.user-item:last-child,
.role-item:last-child {
    border-bottom: none;
}

.user-item:hover,
.role-item:hover {
    background-color: #f8f9fa;
}

.checkbox-label {
    display: flex;
    align-items: center;
    cursor: pointer;
    width: 100%;
}

.checkbox-label input[type="checkbox"] {
    margin-right: 12px;
    transform: scale(1.2);
}

.checkmark {
    position: relative;
    margin-right: 12px;
}

.user-info,
.role-info {
    flex: 1;
}

.username,
.rolename {
    font-weight: 500;
    color: #333;
    margin-bottom: 4px;
}

.user-details,
.role-details {
    font-size: 0.9rem;
    color: #666;
}

/* 搜索容器样式 */
.search-container {
    background: #f8f9fa;
    border: 1px solid #dee2e6;
    border-radius: 8px;
    padding: 20px;
    margin-bottom: 20px;
}

.search-form {
    display: flex;
    align-items: end;
    gap: 15px;
    flex-wrap: wrap;
}

.search-form .form-group {
    flex: 1;
    min-width: 200px;
}

.search-form .form-group label {
    display: block;
    margin-bottom: 5px;
    font-weight: 500;
    color: #333;
}

.search-form .form-group input {
    width: 100%;
    padding: 8px 12px;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 14px;
}

.form-actions {
    display: flex;
    gap: 10px;
}

.form-actions .btn {
    white-space: nowrap;
}

/* 空状态样式 */
.empty-state {
    text-align: center;
    padding: 60px 20px;
    color: #999;
}

.empty-state i {
    font-size: 4rem;
    margin-bottom: 20px;
    opacity: 0.3;
}

.empty-state h3 {
    margin-bottom: 10px;
    color: #666;
}

.empty-state p {
    color: #999;
}

/* 加载和错误状态 */
.loading {
    text-align: center;
    padding: 40px;
    color: #666;
}

.loading i {
    font-size: 2rem;
    margin-bottom: 15px;
}

.error {
    text-align: center;
    padding: 40px;
    color: #dc3545;
    background: #f8d7da;
    border: 1px solid #f5c6cb;
    border-radius: 8px;
    margin: 20px 0;
}

/* 必填字段标记 */
.required {
    color: #dc3545;
    font-weight: bold;
}

/* 仪表板样式 */
.dashboard-container {
    padding: 20px;
}

.dashboard-header {
    margin-bottom: 30px;
}

.dashboard-title {
    font-size: 2rem;
    color: #333;
    margin-bottom: 10px;
}

.dashboard-description {
    color: #666;
    font-size: 1.1rem;
}

.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
    margin-bottom: 40px;
}

.stat-card {
    background: white;
    border-radius: 10px;
    padding: 25px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    display: flex;
    align-items: center;
    transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.stat-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 20px rgba(0,0,0,0.15);
}

.stat-icon {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 20px;
    font-size: 1.5rem;
    color: white;
    background: linear-gradient(135deg, #007bff, #0056b3);
}

.stat-content {
    flex: 1;
}

.stat-number {
    font-size: 2rem;
    font-weight: bold;
    color: #333;
    margin-bottom: 5px;
}

.stat-label {
    color: #666;
    font-size: 0.9rem;
}

.quick-actions {
    margin-bottom: 40px;
}

.quick-actions h4 {
    font-size: 1.3rem;
    color: #333;
    margin-bottom: 20px;
    border-bottom: 2px solid #007bff;
    padding-bottom: 10px;
}

.action-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 20px;
}

.action-card {
    background: white;
    border-radius: 10px;
    padding: 20px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    display: flex;
    align-items: center;
    cursor: pointer;
    transition: all 0.2s ease;
}

.action-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 20px rgba(0,0,0,0.15);
    background: #f8f9fa;
}

.action-icon {
    width: 50px;
    height: 50px;
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 15px;
    font-size: 1.2rem;
    color: white;
    background: linear-gradient(135deg, #28a745, #20c997);
}

.action-content {
    flex: 1;
}

.action-title {
    font-size: 1.1rem;
    font-weight: 600;
    color: #333;
    margin-bottom: 5px;
}

.action-description {
    color: #666;
    font-size: 0.9rem;
}

.system-info {
    background: white;
    border-radius: 10px;
    padding: 25px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.system-info h4 {
    font-size: 1.3rem;
    color: #333;
    margin-bottom: 20px;
    border-bottom: 2px solid #007bff;
    padding-bottom: 10px;
}

.info-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 15px;
}

.info-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 10px 0;
    border-bottom: 1px solid #eee;
}

.info-item:last-child {
    border-bottom: none;
}

.info-label {
    font-weight: 500;
    color: #666;
}

.info-value {
    color: #333;
    font-weight: 600;
}

/* 地址解析页面样式 */
.address-parse-container {
    padding: 20px;
}

.parse-header {
    margin-bottom: 30px;
}

.parse-title {
    font-size: 2rem;
    color: #333;
    margin-bottom: 10px;
}

.parse-description {
    color: #666;
    font-size: 1.1rem;
}

.parse-actions {
    display: flex;
    gap: 15px;
    margin-bottom: 30px;
    flex-wrap: wrap;
}

.parse-content {
    background: white;
    border-radius: 10px;
    padding: 25px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.data-section h4 {
    font-size: 1.3rem;
    color: #333;
    margin-bottom: 20px;
    border-bottom: 2px solid #007bff;
    padding-bottom: 10px;
}

.data-count {
    background: #007bff;
    color: white;
    padding: 2px 8px;
    border-radius: 12px;
    font-size: 0.8rem;
    margin-left: 10px;
}

/* 文件管理页面样式 */
.file-management-container {
    padding: 20px;
}

.file-header {
    margin-bottom: 30px;
}

.file-title {
    font-size: 2rem;
    color: #333;
    margin-bottom: 10px;
}

.file-description {
    color: #666;
    font-size: 1.1rem;
}

.upload-section,
.pending-files-section,
.uploaded-files-section {
    background: white;
    border-radius: 10px;
    padding: 25px;
    margin-bottom: 20px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.upload-section h4,
.pending-files-section h4,
.uploaded-files-section h4 {
    font-size: 1.3rem;
    color: #333;
    margin-bottom: 20px;
    border-bottom: 2px solid #007bff;
    padding-bottom: 10px;
}

.upload-actions {
    display: flex;
    gap: 15px;
    margin-top: 20px;
    flex-wrap: wrap;
}

.file-count {
    background: #007bff;
    color: white;
    padding: 2px 8px;
    border-radius: 12px;
    font-size: 0.8rem;
    margin-left: 10px;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .form-row {
        flex-direction: column;
        gap: 0;
    }

    .table-header {
        flex-direction: column;
        align-items: stretch;
    }

    .table-actions {
        justify-content: center;
    }

    .pagination {
        flex-wrap: wrap;
        gap: 5px;
    }

    .modal {
        margin: 10px;
        max-height: calc(100vh - 20px);
    }

    .modal-header,
    .modal-body,
    .modal-footer {
        padding: 20px;
    }

    .search-form {
        flex-direction: column;
        align-items: stretch;
    }

    .search-form .form-group {
        min-width: auto;
    }

    .form-actions {
        justify-content: center;
    }
}
