#设置数据库mysql的数据配置信息
spring:


  datasource:
    driver-class-name: com.mysql.cj.jdbc.Driver
    url: ***********************************************************************************************************************
    username: qingshuihe
    password: qingshuihe
#  设置springboot内嵌的tomcat支持上传的单个（file）文件以及多个（request）文件大小限制
  servlet:
    multipart:
      max-file-size: 1024MB
      max-request-size: 1024MB

  kafka:
    bootstrap-servers: 127.0.0.1:9092
    template:
      default-topic: qingshuihe
    producer:
      key-serializer: org.apache.kafka.common.serialization.StringSerializer
      value-serializer: org.apache.kafka.common.serialization.StringSerializer
    consumer:
      group-id: qingshuiheGroup
      enable-auto-commit: true
      auto-commit-interval: 1000
      key-deserializer: org.apache.kafka.common.serialization.StringDeserializer
      value-deserializer: org.apache.kafka.common.serialization.StringDeserializer
    properties:
      security:
        protocol: SASL_PLAINTEXT
      sasl:
        mechanism: PLAIN
        jaas:
          config: 'org.apache.kafka.common.security.scram.ScramLoginModule required username="qingshuihe" password="qingshuihe";'
  mail:
    username: <EMAIL>
#    获取这个password是在邮箱开启stmp功能，发送短信后获取的腾讯返回的密码，不是qq密码
    password: aexinfxhiusobdcd
    host: smtp.qq.com
    properties:
      mail:
        smtp:
          ssl: true
#  elasticsearch:
#    rest:
#      uris: http://localhost:9200

#设置redis的配置信息
redis-config:
  pool:
    password:
    host: 127.0.0.1
    port: 6379
    #设置最大连接数，默认值为8.如果赋值为-1，则表示不限制；
    maxTotal: 100
    #最大空闲连接数 默认值：8
    maxIdle: 10
    #最小空闲连接数 默认值：0
    minIdle: 10
    #当资源池连接用尽后，获取Jedis连接的最大等待时间，单位毫秒，默认值为-1，表示永不超时。如果超过等待时间，则直接抛出JedisConnectionException
    maxWaitMillis: 10000
    #连接空闲多久后释放, 当空闲时间大于该值 且 空闲连接大于最大空闲连接数 时直接释放, 默认值:-1L
    softMinEvictableIdleTimeMillis: 10000
    #向资源池借用连接时，是否做连接有效性检测，无效连接会被移除，默认值：false ，业务量很大时建议为false，因为会多一次ping的开销
    testOnBorrow: true
    #向资源池归还连接时，是否做连接有效性检测，无效连接会被移除，默认值：false，业务量很大时建议为false，因为会多一次ping的开销
    testOnReturn: true
    #自动测试池中的空闲连接是否都是可用连接(是否开启空闲资源监测，默认值：false)
    testWhileIdle: true
    #当需要对空闲资源进行监测时， testWhileIdle 参数开启后与下列几个参数(timeBetweenEvictionRunsMillis minEvictableIdleTimeMillis numTestsPerEvictionRun )组合完成监测任务。
    #空闲资源的检测周期，单位为毫秒，默认值：-1，表示不检测，建议设置一个合理的值，周期性运行监测任务
    timeBetweenEvictionRunsMillis: 30000
    #资源池中资源最小空闲时间，单位为毫秒，默认值：30分钟（1000 60L 30L）=1800000，当达到该值后空闲资源将被移除，建议根据业务自身设定
    minEvictableIdleTimeMillis: 1800000
    #做空闲资源检测时，每次的采样数，默认值：3，可根据自身应用连接数进行微调，如果设置为 -1，表示对所有连接做空闲监测
    numTestsPerEvictionRun: 3
    #当资源池用尽后，调用者是否要等待(阻塞)。默认值：true，当为true时，maxWaitMillis参数才会生效，建议使用默认值, false会报异常
    blockWhenExhausted: true
    #是否启用pool的jmx管理功能, 默认true
    jmxEnabled: true
#设置mybatis-plus的配置信息，
mybatis-plus:
  configuration:
#    设置打印sql信息到控制台
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl
#    设置逻辑删除
  global-config:
    db-config:
      logic-delete-field: enable
      logic-delete-value: 1
file:
  upload:
    hostPath: D:\uploadFiles
#重置密码的邮件正文设置
resetpw:
  subject: 重置密码
  html: <div><br></div><div>尊敬的REST_USER_NAME，你好</div><div>&nbsp; &nbsp; &nbsp; 你正在进行重置密码操作，如为本人操作，请点击<a href="FRONT_PAGE_SETPW">此链接</a>进行重置密码操作；如非本人操作，请忽略。链接VAILD_TIME分钟内有效。</div><div>
  frontPage: https://qingshuihe.com/initpw/
#  frontPage: https://www.bilibili.com/
  vaildTime: 10
  text: 本次验证码为：VERIFY_CODE，VAILD_TIME分钟内有效
