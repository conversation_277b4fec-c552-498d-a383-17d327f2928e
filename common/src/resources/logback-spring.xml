<?xml version="1.0" encoding="UTF-8"?>
<!-- 日志级别从低到高分为TRACE < DEBUG < INFO < WARN < ERROR < FATAL，比如: 如果设置为WARN，则低于WARN的信息都不会输出 -->
<!-- scan:当此属性设置为true时，配置文档如果发生改变，将会被重新加载，默认值为true -->
<!-- scanPeriod:设置监测配置文档是否有修改的时间间隔，如果没有给出时间单位，默认单位是毫秒。当scan为true时，此属性生效。默认的时间间隔为1分钟。 -->
<!-- debug:当此属性设置为true时，将打印出logback内部日志信息，实时查看logback运行状态。默认值为false。 -->
<configuration scan="true" scanPeriod="10 seconds" debug="false">
    <!-- 尽量别用绝对路径，如果带参数不同容器路径解释可能不同,以下配置参数在pom.xml里 -->
    <!-- 日志级别 -->
    <property name="log.root.level" value="info"/>
    <!-- 其他日志级别 -->
    <property name="log.other.level" value="info"/>
    <!-- 日志路径，这里是相对路径，web项目eclipse下会输出到eclipse的安装目录下，如果部署到linux上的tomcat下，会输出到tomcat/bin目录 下 -->
    <property name="log.base" value="logs"/>
    <!-- 模块名称， 影响日志配置名，日志文件名 -->
    <property name="log.moduleName" value="2020-edge-barnacle-application"/>
    <!-- 日志文件大小 -->
    <property name="log.max.size" value="500MB"/>
    <!-- 定义日志输出格式-->
    <property name="log.pattern"
              value="%date{ISO8601} %-1level [%thread] %logger{56}.%method:%L -%msg%n"/>
    <property name="log.charset" value="UTF-8"/>

    <!--控制台输出 -->
    <appender name="stdout" class="ch.qos.logback.core.ConsoleAppender">
        <!-- 用来定义日志的格式 -->
        <encoder class="ch.qos.logback.classic.encoder.PatternLayoutEncoder">
                <pattern>${log.pattern}</pattern>
            <charset>${log.charset}</charset>
        </encoder>
    </appender>

    <!-- 日志文件输出 -->
    <appender name="infoFile" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <!-- 设置日志不超过${log.max.size}时的保存路径，注意如果 是web项目会保存到Tomcat的bin目录 下 -->
        <file>${log.base}/info/${log.moduleName}_info.log</file>
        <!-- 日志输出的文件的格式  -->
        <encoder class="ch.qos.logback.classic.encoder.PatternLayoutEncoder">
            <springProfile name="dev">
                <pattern>这是开发环境的日志：%date{yyyy-MM-dd HH:mm:ss.SSS} %-5level [%thread]%logger{56}.%method:%L -%msg%n</pattern>
            </springProfile>
            <springProfile name="stg">
                <pattern>这是测试环境的日志：%date{yyyy-MM-dd HH:mm:ss.SSS} %-5level [%thread]%logger{56}.%method:%L -%msg%n</pattern>
            </springProfile>
            <charset>${log.charset}</charset>
        </encoder>
        <!-- 滚动记录文件，先将日志记录到指定文件，当符合某个条件时，将日志记录到其他文件。-->
        <rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
            <!--按天回滚-->
            <fileNamePattern>${log.base}/info/archive/${log.moduleName}_info_%d{yyyy-MM-dd}.%i.log.zip</fileNamePattern>
            <!--日志最大存储天数-->
            <maxHistory>60</maxHistory>
            <!-- 当天的日志大小 超过${log.max.size}时,压缩日志并保存 -->
            <maxFileSize>${log.max.size}</maxFileSize>
        </rollingPolicy>
        <!--过滤器，只记录INFO级别的日志-->
        <filter class="ch.qos.logback.classic.filter.LevelFilter">
            <level>INFO</level>
            <onMatch>ACCEPT</onMatch>
            <onMismatch>DENY</onMismatch>
        </filter>
    </appender>

    <!-- 日志文件输出 -->
    <appender name="warnFile" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <!-- 设置日志不超过${log.max.size}时的保存路径，注意如果 是web项目会保存到Tomcat的bin目录 下 -->
        <file>${log.base}/warn/${log.moduleName}_warn.log</file>
        <!-- 日志输出的文件的格式  -->
        <encoder class="ch.qos.logback.classic.encoder.PatternLayoutEncoder">
            <pattern>%date{yyyy-MM-dd HH:mm:ss.SSS} %-5level [%thread]%logger{56}.%method:%L -%msg%n</pattern>
        </encoder>
        <!-- 滚动记录文件，先将日志记录到指定文件，当符合某个条件时，将日志记录到其他文件。-->
        <rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
            <fileNamePattern>${log.base}/warn/archive/${log.moduleName}_warn_%d{yyyy-MM-dd}.%i.log.zip
            </fileNamePattern>
            <!--日志最大存储天数-->
            <maxHistory>60</maxHistory>
            <!-- 当天的日志大小 超过${log.max.size}时,压缩日志并保存 -->
            <maxFileSize>${log.max.size}</maxFileSize>
        </rollingPolicy>
        <filter class="ch.qos.logback.classic.filter.LevelFilter">
            <level>WARN</level>
            <onMatch>ACCEPT</onMatch>
            <onMismatch>DENY</onMismatch>
        </filter>
    </appender>

    <!-- 日志文件输出 -->
    <appender name="errorFile" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <!-- 设置日志不超过${log.max.size}时的保存路径，注意如果 是web项目会保存到Tomcat的bin目录 下 -->
        <file>${log.base}/error/${log.moduleName}_error.log</file>
        <!-- 日志输出的文件的格式  -->
        <encoder class="ch.qos.logback.classic.encoder.PatternLayoutEncoder">
            <springProfile name="dev">
                <pattern>%date{yyyy-MM-dd HH:mm:ss.SSS}====== %-5level [%thread]%logger{56}.%method:%L -%msg%n</pattern>
            </springProfile>
            <springProfile name="!dev">
                <pattern>%date{yyyy-MM-dd HH:mm:ss.SSS} %-5level [%thread]%logger{56}.%method:%L -%msg%n</pattern>
            </springProfile>
        </encoder>
        <!-- 滚动记录文件，先将日志记录到指定文件，当符合某个条件时，将日志记录到其他文件。-->
        <rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
            <fileNamePattern>${log.base}/error/archive/${log.moduleName}_error_%d{yyyy-MM-dd}.%i.log.zip</fileNamePattern>
            <!--日志最大存储天数-->
            <maxHistory>60</maxHistory>
            <!-- 当天的日志大小 超过${log.max.size}时,压缩日志并保存 -->
            <maxFileSize>${log.max.size}</maxFileSize>
        </rollingPolicy>
        <filter class="ch.qos.logback.classic.filter.LevelFilter">
            <level>ERROR</level>
            <onMatch>ACCEPT</onMatch>
            <onMismatch>DENY</onMismatch>
        </filter>
    </appender>


    <!-- 为某个包下的所有类的指定Appender 这里也可以指定类名称例如：com.aa.bb.ClassName -->
    <logger name="org.springframework.aop.framework.CglibAopProxy" additivity="false">
        <level value="info" />
        <appender-ref ref="stdout" />
    </logger>
    <!-- root将级别为“DEBUG”及大于“DEBUG”的日志信息交给已经配置好的名为“Console”的appender处理，“Console”appender将信息打印到Console -->
    <root level="info">
        <appender-ref ref="stdout" /> <!-- 标识这个appender将会添加到这个logger -->
        <appender-ref ref="infoFile" />
        <appender-ref ref="warnFile" />
        <appender-ref ref="errorFile" />
    </root>
</configuration>
