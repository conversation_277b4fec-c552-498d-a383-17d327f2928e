package convert;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;

import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

public class TxtToJsonConverter {

    public static List<PersonDTO> convertTxtToJson(String filePath) throws IOException {
        ObjectMapper objectMapper = new ObjectMapper();
        String fileContent = new String(Files.readAllBytes(Paths.get(filePath)));

        //处理JSON数组的情况，如果你的txt文件内容是一个JSON数组，则使用以下代码
        if (fileContent.startsWith("[") && fileContent.endsWith("]")) {
            try {
                return objectMapper.readValue(fileContent, new TypeReference<List<PersonDTO>>() {});
            } catch (IOException e) {
                throw new IOException("Failed to parse JSON array: " + e.getMessage());
            }
        } else {
            //处理JSON对象的情况，如果你的txt文件内容是多个JSON对象，则使用以下代码
            String[] jsonObjects = fileContent.split("\\},\\{");
            List<PersonDTO> personDTOs = new ArrayList<>();
            for (String jsonObject : jsonObjects) {
                jsonObject = "{" + jsonObject.trim() + "}"; //修复split后缺失的{}
                try {
                    PersonDTO personDTO = objectMapper.readValue(jsonObject, PersonDTO.class);
                    personDTOs.add(personDTO);
                } catch (IOException e) {
                    throw new IOException("Failed to parse JSON object: " + e.getMessage());
                }
            }
            return personDTOs;
        }
    }


    public static void main(String[] args) {
        String filePath = "C:\\Users\\<USER>\\Desktop\\123.txt"; // Correct path with double backslashes //替换成你的文件路径
        try {
            List<PersonDTO> personList = convertTxtToJson(filePath);
            List<Long> collect = personList.stream().map(PersonDTO::getGul_id).collect(Collectors.toList());

            System.out.println(collect);
        } catch (IOException e) {
            e.printStackTrace();
            System.err.println("Error converting TXT to JSON: " + e.getMessage());
        }
    }
}