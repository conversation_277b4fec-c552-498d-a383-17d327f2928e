package convert;

import lombok.Data;

/**
 * @Description:
 * @Author: shl
 * @Date: 2024/11/1
 **/
@Data
public class PersonDTO {
    public long gul_id;
    public long group_id;
    public long trace_id;
    public String plan_name;
    public String real_name;
    public String cert_type_code;
    public String cert_type;
    public String cert_code;
    public String birth;
    public String relation_code;
    public String relation;
    public String add_type;
    public String main_real_name;
    public String main_cert_code;
    public String start_time;
    public String end_time;
    public String modify_end_time;
    public boolean has_relation;
    public boolean is_auto_delete;
    public boolean is_delete;
    public long plan_id;
    public long plan_config_id;
    public long uid;
    public long subject_id;
    public long online_insured_id;

    //构造函数，getter和setter方法 (省略为了简洁)
    public PersonDTO() {} //需要一个无参构造函数用于JSON反序列化

    // ... getters and setters ...
}
