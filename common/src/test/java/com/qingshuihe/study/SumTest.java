package com.qingshuihe.study;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.qingshuihe.insgeek.Entity;
import org.junit.jupiter.api.Test;
import org.springframework.util.CollectionUtils;

import java.io.File;
import java.io.IOException;
import java.nio.file.Files;
import java.time.format.DateTimeParseException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

class SumTest {

    @Test
    void lengthOfArray() {
        String s1 = "hello";
        String s2 = new String("hello");
        String s5 = new String("hello");
        System.out.println(s2==s5);
        String s3 = new String(s2);
        String s4 = new String("hello,world");
        s2 = s2+",world";
        System.out.println(s1);
        System.out.println(s2);
        System.out.println(s3);
        System.out.println(s2==s4);
    }


    @Test
    void jump() {
//        int[] nums = {2,3,1,1,4};
        int[] nums = {2,3,0,1,4};
        System.out.println(Sum.jump(nums));
    }

    @Test
    void canCompleteCircuit() {

       int[] gas = {1,2,3,4,5};
       int[] cost = {3,4,5,1,2};
       System.out.println(Sum.canCompleteCircuit(gas,cost));

    }

    @Test
    void isPalindrome() {
        String s = "A man, a plan, a canal: Panama";
        System.out.println(Sum.isPalindrome(s));

    }

    @Test
    void maxArea() {
//        int[] height = {1,1};
        int[] height = {1,8,6,2,5,4,8,3,7};
        System.out.println(Sum.maxArea(height));


    }

    @Test
    void addTwoNumbersHelper() {
        ListNode node1 = new ListNode(1);
        ListNode node2 = new ListNode(9);
        ListNode node3 = new ListNode(9);
        ListNode node4 = new ListNode(9);
        ListNode node5 = new ListNode(9);
        ListNode node6 = new ListNode(9);
        ListNode node7 = new ListNode(9);
        ListNode node8 = new ListNode(9);
        ListNode node9 = new ListNode(9);
        ListNode node10 = new ListNode(9);
        node1.next = node2;
        node2.next = node3;
        node3.next = node4;
        node4.next = node5;
        node5.next = node6;
        node6.next = node7;
        node7.next = node8;
        node8.next = node9;
        node9.next = node10;
        node10.next = null;

        ListNode node11 = new ListNode(9);

        System.out.println(Sum.addTwoNumbers(node1,node11));
    }

    @Test
    void reverseBetween() {
        ListNode node1 = new ListNode(1);
        ListNode node2 = new ListNode(2);
        ListNode node3 = new ListNode(3);
        ListNode node4 = new ListNode(4);
        ListNode node5 = new ListNode(5);
        node1.next = node2;
        node2.next = node3;
        node3.next = node4;
        node4.next = node5;
        node5.next = null;
        System.out.println(Sum.reverseBetween(node1,1,5));

    }

    @Test
    void removeNthFromEnd() {

        ListNode node1 = new ListNode(1);
        ListNode node2 = new ListNode(2);
        ListNode node3 = new ListNode(3);
        ListNode node4 = new ListNode(4);
        ListNode node5 = new ListNode(5);
        node1.next = node2;
        node2.next = node3;
        node3.next = node4;
        node4.next = node5;
        node5.next = null;
        System.out.println(Sum.removeNthFromEnd(node5,1).val);

    }

    @Test
    void canNext() {
        List<Integer> personalDTOS = (List<Integer>) null;
        if (CollectionUtils.isEmpty(personalDTOS)) {
            System.out.println(true);
        }
    }


    @Test
    void testList(){
        List<Integer> numbers = Arrays.asList(1, 2, 0, 4, 5);

        for (int number : numbers) {
            System.out.println("number: " + number);
            try {
                int result = 10 / number;
                System.out.println("Result: " + result);
            } catch (DateTimeParseException e) {
                System.out.println("Exception caught: " + e.getMessage());
            }
        }
    }

    @Test
    void testList_1(){
        List<Entity> entities = extractEntities("C:\\Users\\<USER>\\Desktop\\新文件 1.txt");
        List<Long> userOrderId = entities.stream().map(Entity::getUserOrderId).collect(Collectors.toList());
        System.out.println(userOrderId);
    }

    public static List<Entity> extractEntities(String filePath) {
        List<Entity> entities = new ArrayList<>();
        ObjectMapper objectMapper = new ObjectMapper();

        try {
            File file = new File(filePath);
            String content = new String(Files.readAllBytes(file.toPath()));
            JsonNode rootNode = objectMapper.readTree(content);

            if (rootNode.isArray()) {
                for (JsonNode node : rootNode) {
                    Entity entity = objectMapper.treeToValue(node, Entity.class);
                    entities.add(entity);
                }
            }
        } catch (IOException e) {
            System.err.println("Error reading file: " + e.getMessage());
        }

        return entities;
    }



}