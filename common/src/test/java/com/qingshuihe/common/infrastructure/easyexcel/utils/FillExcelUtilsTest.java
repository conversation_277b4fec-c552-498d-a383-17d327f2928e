package com.qingshuihe.common.infrastructure.easyexcel.utils;
import com.alibaba.excel.util.DateUtils;
import com.qingshuihe.common.infrastructure.easyexcel.dto.FillExcelDTO;
import com.qingshuihe.common.infrastructure.easyexcel.dto.InsgeekIncreaseBatchDTO;
import org.junit.jupiter.api.Test;

import javax.xml.bind.ValidationException;
import java.text.ParseException;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.UUID;

class FillExcelUtilsTest {


    @Test
    void fillExcel() throws ValidationException, ParseException {
        String uuid = UUID.randomUUID().toString();
        String templatePath = "D:/worktools/project_template/batch_increase_template.xlsx";
        String exprotPath = "D:/worktools/project_template/" + uuid + ".xlsx";
        FillExcelDTO fillExcelDTO = new FillExcelDTO(templatePath, exprotPath);
        List<InsgeekIncreaseBatchDTO> fillObjectList =buildIncreaseBathDTO(2000);
        fillExcelDTO.setFillObjectList(fillObjectList);
        FillExcelUtils.fillExcel(fillExcelDTO);


    }

    private List<InsgeekIncreaseBatchDTO> buildIncreaseBathDTO(int n) throws ParseException {
        String date = DateUtils.format(new Date(), DateUtils.DATE_FORMAT_14);

        List<InsgeekIncreaseBatchDTO> fillObjectList = new ArrayList<>();
        for (int i = 0; i < n; i++) {
            InsgeekIncreaseBatchDTO insgeekIncreaseBatchDTO = new InsgeekIncreaseBatchDTO();
            insgeekIncreaseBatchDTO.setName("张三");
            insgeekIncreaseBatchDTO.setCertType("护照号");
            insgeekIncreaseBatchDTO.setCertCode("G"+date+i);
            insgeekIncreaseBatchDTO.setGender("男");
            insgeekIncreaseBatchDTO.setBirthDate(DateUtils.parseDate("1991-01-01",DateUtils.DATE_FORMAT_10));
            insgeekIncreaseBatchDTO.setPhoneNum(15055556666l);
            insgeekIncreaseBatchDTO.setYiBaoType("城镇职工（含在职和退休）");
            insgeekIncreaseBatchDTO.setYiBaoAddress("北京 北京市");
            insgeekIncreaseBatchDTO.setRelation("本人");
            insgeekIncreaseBatchDTO.setMainName("");
            insgeekIncreaseBatchDTO.setMainCertCode("");
            insgeekIncreaseBatchDTO.setPlanName("意外伤害理赔生效方案");
            insgeekIncreaseBatchDTO.setYusuan("");
            insgeekIncreaseBatchDTO.setStartTime(DateUtils.parseDate("2024-09-12",DateUtils.DATE_FORMAT_10));
            insgeekIncreaseBatchDTO.setEndTime(DateUtils.parseDate("2025-08-03",DateUtils.DATE_FORMAT_10));
            fillObjectList.add(insgeekIncreaseBatchDTO);
        }
        return fillObjectList;
    }
}