package com.qingshuihe.common.infrastructure.easyexcel.utils;

import com.alibaba.fastjson2.JSON;
import org.junit.jupiter.api.Test;

import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;
class SplitExcelUtilsTest {

    @Test
    void splitExcel() throws IOException {
        String exprotPath = "D:/worktools/project_template/2024-10-17-52eab2d1-988c-4243-b6b9-71b9db09e0c8.xlsx";
        List<List<List<Object>>> lists = SplitExcelUtils.splitExcel(exprotPath,200);
        System.out.println(lists.size());

    }

    @Test
    void test1 (){
        List<String> list = Arrays.asList(
                "6149979393：5311432102,6149979393",
                "6149979393：5311432102,6149979393",
                "6149979393：5311432102,6149979393",
                "6149979393：5311432102,6149979393",
                "6149979393：5311432102,6149979393",
                "6149979393：5311432102,6149979393",
                "6149979393：5311432102,6149979393",
                "6149979393：5311432102,6149979393",
                "6149979393：5311432102,6149979393",
                "5311433086：5311433082,5311433084,5311433086",
                "5311433086：5311433082,5311433084,5311433086",
                "5311433086：5311433082,5311433084,5311433086",
                "5267630547：5267630547,5311433300,5311434434",
                "5219145900：5219145900,5311432395",
                "5219145900：5219145900,5311432395",
                "5219249494：5219249494,5311432395",
                "5219249494：5219249494,5311432395",
                "5219339164：5219339164,5311432395",
                "5227154641：5227154641,5311434300",
                "5229338810：5229338810,5311434302",
                "5229338810：5229338810,5311434302",
                "5229338810：5229338810,5311434302",
                "5229338810：5229338810,5311434302",
                "5229413122：5229413122,5311434304",
                "5233755635：5233755635,5311434308",
                "5235244555：5235244555,5311434310,5311434312",
                "5235244555：5235244555,5311434310,5311434312",
                "5241104479：5241104479,5311434314",
                "5241048225：5241048225,5311434318",
                "5241048225：5241048225,5311434318",
                "5241114095：5241114095,5311434316",
                "5241114095：5241114095,5311434316",
                "5241109269：5241109269,5311434320",
                "5242243198：5242243198,5311434330",
                "5242095231：5242095231,5311434322,5311434324",
                "5242095231：5242095231,5311434322,5311434324",
                "5242241220：5242241220,5311434326,5311434328",
                "5242241220：5242241220,5311434326,5311434328",
                "5242306085：5242306085,5311434332",
                "5242317594：5242317594,5311434334,5311434336",
                "5242317594：5242317594,5311434334,5311434336",
                "5242827792：5242827792,5311434338",
                "5243097429：5243097429,5311432395",
                "5243097429：5243097429,5311432395",
                "5243097429：5243097429,5311432395",
                "5243097429：5243097429,5311432395",
                "5243097429：5243097429,5311432395",
                "5245460889：5245460889,5311432395",
                "5245460889：5245460889,5311432395",
                "5245460889：5245460889,5311432395",
                "5245460889：5245460889,5311432395",
                "5245460889：5245460889,5311432395",
                "5245460889：5245460889,5311432395",
                "5245460889：5245460889,5311432395",
                "5245460889：5245460889,5311432395",
                "5245642503：5245642503,5311434340",
                "5245711150：5245711150,5311432395",
                "5245711150：5245711150,5311432395",
                "5245711150：5245711150,5311432395",
                "5245711150：5245711150,5311432395",
                "5245711150：5245711150,5311432395",
                "5245711150：5245711150,5311432395",
                "5245711150：5245711150,5311432395",
                "5245711150：5245711150,5311432395",
                "5246408075：5246408075,5311434342",
                "5246408075：5246408075,5311434342",
                "5246408075：5246408075,5311434342",
                "5246352741：5246352741,5311432395",
                "5246352741：5246352741,5311432395",
                "5246352741：5246352741,5311432395",
                "5246352741：5246352741,5311432395",
                "5246352741：5246352741,5311432395",
                "5246352741：5246352741,5311432395",
                "5246352741：5246352741,5311432395",
                "5248239052：5248239052,5311434344",
                "5245711150：5245711150,5311432395",
                "5250676791：5250676791,5311434346,5311434348",
                "5250676791：5250676791,5311434346,5311434348",
                "5250690144：5250690144,5311434350",
                "5250693813：5250693813,5311434352,5311434354",
                "5250693813：5250693813,5311434352,5311434354",
                "5250697682：5250697682,5311434356",
                "5251854364：5251854364,5311432395",
                "5251854364：5251854364,5311432395",
                "5252103067：5252103067,5311434358",
                "5252183379：5252183379,5311434360,5311434362",
                "5252183379：5252183379,5311434360,5311434362",
                "5252385559：5252385559,5311434364,5311434366,5311434368",
                "5252385559：5252385559,5311434364,5311434366,5311434368",
                "5252385559：5252385559,5311434364,5311434366,5311434368",
                "5253236156：5253236156,5311434370",
                "5265325247：5265325247,5311434416",
                "5255710947：5255710947,5311432395",
                "5255710947：5255710947,5311432395",
                "5255710947：5255710947,5311432395",
                "5255739292：5255739292,5311432395",
                "5255739292：5255739292,5311432395",
                "5255739292：5255739292,5311432395",
                "5255739292：5255739292,5311432395",
                "5255739292：5255739292,5311432395",
                "5255799758：5255799758,5311432395",
                "5255799758：5255799758,5311432395",
                "5255799758：5255799758,5311432395",
                "5255799758：5255799758,5311432395",
                "5255869168：5255869168,5311432395",
                "5255869168：5255869168,5311432395",
                "5256024329：5256024329,5311432395",
                "5256024329：5256024329,5311432395",
                "5256024329：5256024329,5311432395",
                "5256024329：5256024329,5311432395",
                "5256034139：5256034139,5311432395",
                "5256034139：5256034139,5311432395",
                "5256034139：5256034139,5311432395",
                "5256044369：5256044369,5311432395",
                "5256044369：5256044369,5311432395",
                "5256109178：5256109178,5311432395",
                "5256109178：5256109178,5311432395",
                "5256117887：5256117887,5311432395",
                "5256117887：5256117887,5311432395",
                "5256117887：5256117887,5311432395",
                "5256117887：5256117887,5311432395",
                "5256180489：5256180489,5311432395",
                "5256180489：5256180489,5311432395",
                "5256180489：5256180489,5311432395",
                "5256180489：5256180489,5311432395",
                "5256213772：5256213772,5311432395",
                "5256213772：5256213772,5311432395",
                "5256213772：5256213772,5311432395",
                "5256213772：5256213772,5311432395",
                "5256213772：5256213772,5311432395",
                "5256275497：5256275497,5311432395",
                "5256275497：5256275497,5311432395",
                "5256275497：5256275497,5311432395",
                "5256322419：5256322419,5311432395",
                "5256322419：5256322419,5311432395",
                "5256322419：5256322419,5311432395",
                "5256322419：5256322419,5311432395",
                "5256410772：5256410772,5311432395",
                "5256410772：5256410772,5311432395",
                "5256410772：5256410772,5311432395",
                "5256410772：5256410772,5311432395",
                "5256432304：5256432304,5311432395",
                "5256432304：5256432304,5311432395",
                "5256432304：5256432304,5311432395",
                "5256432304：5256432304,5311432395",
                "5256432304：5256432304,5311432395",
                "5256432304：5256432304,5311432395",
                "5256432304：5256432304,5311432395",
                "5256432304：5256432304,5311432395",
                "5256575951：5256575951,5311432395",
                "5256575951：5256575951,5311432395",
                "5256606419：5256606419,5311432395",
                "5256606419：5256606419,5311432395",
                "5256636883：5256636883,5311432395",
                "5256636883：5256636883,5311432395",
                "5256636883：5256636883,5311432395",
                "5256764100：5256764100,5311432395",
                "5256764100：5256764100,5311432395",
                "5261848651：5261848651,5311434372,5311434374,5311434376",
                "5261848651：5261848651,5311434372,5311434374,5311434376",
                "5261848651：5261848651,5311434372,5311434374,5311434376",
                "5261981273：5261981273,5311434378,5311434380",
                "5261981273：5261981273,5311434378,5311434380",
                "5264239047：5264239047,5311434392",
                "5264239047：5264239047,5311434392",
                "5264239047：5264239047,5311434392",
                "5264239047：5264239047,5311434392",
                "5264239047：5264239047,5311434392",
                "5264239047：5264239047,5311434392",
                "5264239047：5264239047,5311434392",
                "5264239047：5264239047,5311434392",
                "5264239047：5264239047,5311434392",
                "5264239047：5264239047,5311434392",
                "5264631077：5264631077,5311434396",
                "5264631077：5264631077,5311434396",
                "5264631077：5264631077,5311434396",
                "5264631077：5264631077,5311434396",
                "5264631077：5264631077,5311434396",
                "5264631077：5264631077,5311434396",
                "5264631077：5264631077,5311434396",
                "5264631077：5264631077,5311434396",
                "5264631077：5264631077,5311434396",
                "5264631077：5264631077,5311434396",
                "5264434028：5264434028,5311434394",
                "5264434028：5264434028,5311434394",
                "5264434028：5264434028,5311434394",
                "5264434028：5264434028,5311434394",
                "5264434028：5264434028,5311434394",
                "5264434028：5264434028,5311434394",
                "5264434028：5264434028,5311434394",
                "5264434028：5264434028,5311434394",
                "5264434028：5264434028,5311434394",
                "5264434028：5264434028,5311434394",
                "5265285304：5265285304,5311434412",
                "5265269963：5265269963,5311434406",
                "5265319039：5265319039,5311434414",
                "5272392995：5272392995,5311434446",
                "5264039644：5264039644,5311434382",
                "5264048128：5264048128,5311434384",
                "5264064692：5264064692,5311434386",
                "5264092179：5264092179,5311434388",
                "5264100007：5264100007,5311434390",
                "5273559710：5273559710,5311434418",
                "5264653141：5264653141,5311434398,5311434400",
                "5264653141：5264653141,5311434398,5311434400",
                "5265168887：5265168887,5311434408",
                "5265232266：5265232266,5311434410",
                "5266939236：5266939236,5311434420,5311434422,5311434424",
                "5266939236：5266939236,5311434420,5311434422,5311434424",
                "5266939236：5266939236,5311434420,5311434422,5311434424",
                "5267149635：5267149635,5311434426,5311434428,5311434430,5311434432",
                "5267149635：5267149635,5311434426,5311434428,5311434430,5311434432",
                "5267149635：5267149635,5311434426,5311434428,5311434430,5311434432",
                "5267149635：5267149635,5311434426,5311434428,5311434430,5311434432",
                "5267630547：5267630547,5311433300,5311434434",
                "5269974179：5269974179,5311432395",
                "5269974179：5269974179,5311432395",
                "5269974179：5269974179,5311432395",
                "5269974179：5269974179,5311432395",
                "5269974179：5269974179,5311432395",
                "5269974179：5269974179,5311432395",
                "5269974179：5269974179,5311432395",
                "5269974179：5269974179,5311432395",
                "5271098356：5271098356,5311434436,5311434438,5311434440,5311434442",
                "5271098356：5271098356,5311434436,5311434438,5311434440,5311434442",
                "5271098356：5271098356,5311434436,5311434438,5311434440,5311434442",
                "5271098356：5271098356,5311434436,5311434438,5311434440,5311434442",
                "5271423357：5271423357,5311434444",
                "5271423357：5271423357,5311434444",
                "5272652648：5272652648,5311434448",
                "5272652648：5272652648,5311434448",
                "5273774674：5273774674,5311434452",
                "5273774674：5273774674,5311434452",
                "5274225987：5274225987,5311432395",
                "5274289054：5274289054,5311432395",
                "5274289054：5274289054,5311432395",
                "5274289054：5274289054,5311432395",
                "5274289054：5274289054,5311432395",
                "5276533536：5276533536,5311434454,5311434456",
                "5276533536：5276533536,5311434454,5311434456",
                "5277666066：5277666066,5311434487",
                "5277666066：5277666066,5311434487",
                "5277666066：5277666066,5311434487",
                "5277666066：5277666066,5311434487",
                "5277666066：5277666066,5311434487",
                "5277666066：5277666066,5311434487",
                "5277146198：5277146198,5311434458",
                "5278115622：5278115622,5311434458,5311434503",
                "5278115622：5278115622,5311434458,5311434503",
                "5278115622：5278115622,5311434458,5311434503",
                "5278115622：5278115622,5311434458,5311434503",
                "5278115622：5278115622,5311434458,5311434503",
                "5278115622：5278115622,5311434458,5311434503",
                "5277491584：5277491584,5311434461,5311434463,5311434465",
                "5277491584：5277491584,5311434461,5311434463,5311434465",
                "5277491584：5277491584,5311434461,5311434463,5311434465",
                "5277520508：5277520508,5311434467",
                "5277521452：5277521452,5311434469",
                "5277542691：5277542691,5311434471,5311434473",
                "5277542691：5277542691,5311434471,5311434473",
                "5277546968：5277546968,5311434475,5311434477",
                "5277546968：5277546968,5311434475,5311434477",
                "5277547711：5277547711,5311434479,5311434481",
                "5277547711：5277547711,5311434479,5311434481",
                "5277550326：5277550326,5311434483,5311434485",
                "5277550326：5277550326,5311434483,5311434485",
                "5277761805：5277761805,5311434489,5311434491",
                "5277761805：5277761805,5311434489,5311434491",
                "5277767873：5277767873,5311434493,5311434495,5311434497",
                "5277767873：5277767873,5311434493,5311434495,5311434497",
                "5277767873：5277767873,5311434493,5311434495,5311434497",
                "5277770682：5277770682,5311434499,5311434501",
                "5277770682：5277770682,5311434499,5311434501",
                "5278289765：5278289765,5311434505",
                "5278876222：5278876222,5311432395",
                "5278876222：5278876222,5311432395",
                "5279201135：5279201135,5311434507,5311434509,5311434511,5311434513",
                "5279201135：5279201135,5311434507,5311434509,5311434511,5311434513",
                "5279201135：5279201135,5311434507,5311434509,5311434511,5311434513",
                "5279201135：5279201135,5311434507,5311434509,5311434511,5311434513",
                "5279240023：5279240023,5311434515",
                "5279243390：5279243390,5311434517,5311434519,5311434521",
                "5279243390：5279243390,5311434517,5311434519,5311434521",
                "5279243390：5279243390,5311434517,5311434519,5311434521",
                "5279249231：5279249231,5311434523,5311434525",
                "5279249231：5279249231,5311434523,5311434525",
                "5281252554：5281252554,5311434527",
                "5281583212：5281583212,5311434529",
                "5281857839：5281857839,5311434531",
                "5282876236：5282876236,5311434533",
                "5282983925：5282983925,5311434535,5311434537",
                "5282983925：5282983925,5311434535,5311434537",
                "5283309972：5283309972,5311434539",
                "5283571341：5283571341,5311434541",
                "5283600128：5283600128,5311434543",
                "5283608728：5283608728,5311434545",
                "5291086654：5291086654,5311434547",
                "5293728406：5293728406,5311434549",
                "5293728406：5293728406,5311434549",
                "5294333582：5294333582,5311434553,5311434555",
                "5294333582：5294333582,5311434553,5311434555",
                "5295679492：5295679492,5311434557",
                "5296226349：5296226349,5311434559",
                "5296548206：5296548206,5311434561",
                "5297079629：5297079629,5311434563",
                "5297414613：5297414613,5311434565,5311434567",
                "5297414613：5297414613,5311434565,5311434567",
                "5297425244：5297425244,5311434569",
                "5297428736：5297428736,5311434571,5311434573",
                "5297428736：5297428736,5311434571,5311434573",
                "5297519782：5297519782,5311434575",
                "5297681359：5297681359,5311434577,5311434579,5311434581,5311434583,5311434585,5311434587,5311434589,5311434591,5311434593,5311434595",
                "5297681359：5297681359,5311434577,5311434579,5311434581,5311434583,5311434585,5311434587,5311434589,5311434591,5311434593,5311434595",
                "5297681359：5297681359,5311434577,5311434579,5311434581,5311434583,5311434585,5311434587,5311434589,5311434591,5311434593,5311434595",
                "5297681359：5297681359,5311434577,5311434579,5311434581,5311434583,5311434585,5311434587,5311434589,5311434591,5311434593,5311434595",
                "5297681359：5297681359,5311434577,5311434579,5311434581,5311434583,5311434585,5311434587,5311434589,5311434591,5311434593,5311434595",
                "5297681359：5297681359,5311434577,5311434579,5311434581,5311434583,5311434585,5311434587,5311434589,5311434591,5311434593,5311434595",
                "5297681359：5297681359,5311434577,5311434579,5311434581,5311434583,5311434585,5311434587,5311434589,5311434591,5311434593,5311434595",
                "5297681359：5297681359,5311434577,5311434579,5311434581,5311434583,5311434585,5311434587,5311434589,5311434591,5311434593,5311434595",
                "5297681359：5297681359,5311434577,5311434579,5311434581,5311434583,5311434585,5311434587,5311434589,5311434591,5311434593,5311434595",
                "5297681359：5297681359,5311434577,5311434579,5311434581,5311434583,5311434585,5311434587,5311434589,5311434591,5311434593,5311434595",
                "5297816363：5297816363,5311434597,5311434599",
                "5297816363：5297816363,5311434597,5311434599",
                "5298042265：5298042265,5311432395",
                "5298900052：5298900052,5311434601,5311434603",
                "5298900052：5298900052,5311434601,5311434603",
                "5298924628：5298924628,5311434605,5311434607",
                "5298924628：5298924628,5311434605,5311434607",
                "5298926821：5298926821,5311434609,5311434611",
                "5298926821：5298926821,5311434609,5311434611",
                "5300053767：5300053767,5311434613",
                "5300068125：5300068125,5311434615,5311434617",
                "5300068125：5300068125,5311434615,5311434617",
                "5300072548：5300072548,5311434619,5311434621",
                "5300072548：5300072548,5311434619,5311434621",
                "5300106494：5300106494,5311434623",
                "5300250042：5300250042,5311434625",
                "5301181994：5301181994,5311434627,5311434629,5311434631,5311434633",
                "5301181994：5301181994,5311434627,5311434629,5311434631,5311434633",
                "5301181994：5301181994,5311434627,5311434629,5311434631,5311434633",
                "5301181994：5301181994,5311434627,5311434629,5311434631,5311434633",
                "5301185725：5301185725,5311434635,5311434637",
                "5301185725：5301185725,5311434635,5311434637",
                "5301190217：5301190217,5311434639,5311434641,5311434643",
                "5301190217：5301190217,5311434639,5311434641,5311434643",
                "5301190217：5301190217,5311434639,5311434641,5311434643",
                "5301192917：5301192917,5311434645,5311434647",
                "5301192917：5301192917,5311434645,5311434647",
                "5301193580：5301193580,5311434649",
                "5301195379：5301195379,5311434655,5311434657,5311434659",
                "5301195379：5301195379,5311434655,5311434657,5311434659",
                "5301195379：5301195379,5311434655,5311434657,5311434659",
                "5301195191：5301195191,5311434651,5311434653",
                "5301195191：5301195191,5311434651,5311434653",
                "5301196621：5301196621,5311434661",
                "5301198671：5301198671,5311434663,5311434665,5311434667,5311434669,5311434671,5311434673,5311434675",
                "5301198671：5301198671,5311434663,5311434665,5311434667,5311434669,5311434671,5311434673,5311434675",
                "5301198671：5301198671,5311434663,5311434665,5311434667,5311434669,5311434671,5311434673,5311434675",
                "5301198671：5301198671,5311434663,5311434665,5311434667,5311434669,5311434671,5311434673,5311434675",
                "5301198671：5301198671,5311434663,5311434665,5311434667,5311434669,5311434671,5311434673,5311434675",
                "5301198671：5301198671,5311434663,5311434665,5311434667,5311434669,5311434671,5311434673,5311434675",
                "5301198671：5301198671,5311434663,5311434665,5311434667,5311434669,5311434671,5311434673,5311434675",
                "5301233832：5301233832,5311434677",
                "5301242350：5301242350,5311434679",
                "5303231188：5303231188,5311434681",
                "5303240499：5303240499,5311434683,5311434685,5311434687",
                "5303240499：5303240499,5311434683,5311434685,5311434687",
                "5303240499：5303240499,5311434683,5311434685,5311434687",
                "5303257628：5303257628,5311434689,5311434691,5311434693,5311434695",
                "5303257628：5303257628,5311434689,5311434691,5311434693,5311434695",
                "5303257628：5303257628,5311434689,5311434691,5311434693,5311434695",
                "5303257628：5303257628,5311434689,5311434691,5311434693,5311434695",
                "5303266239：5303266239,5311434697,5311434699",
                "5303266239：5303266239,5311434697,5311434699",
                "5303362503：5303362503,5311434701,5311434703,5311434705",
                "5303362503：5303362503,5311434701,5311434703,5311434705",
                "5303362503：5303362503,5311434701,5311434703,5311434705",
                "5303372363：5303372363,5311434707,5311434709",
                "5303372363：5303372363,5311434707,5311434709",
                "5305708493：5305708493,5311434549",
                "5304181711：5304181711,5311434711",
                "5304189663：5304189663,5311434713",
                "5304193156：5304193156,5311434715",
                "5304206945：5304206945,5311434717,5311434719,5311434721,5311434723",
                "5304206945：5304206945,5311434717,5311434719,5311434721,5311434723",
                "5304206945：5304206945,5311434717,5311434719,5311434721,5311434723",
                "5304206945：5304206945,5311434717,5311434719,5311434721,5311434723",
                "5304211745：5304211745,5311434725,5311434727",
                "5304211745：5304211745,5311434725,5311434727",
                "5305708493：5305708493,5311434549",
                "5305002943：5305002943,5311434731,5311434733",
                "5305002943：5305002943,5311434731,5311434733",
                "5305013354：5305013354,5311434735,5311434737",
                "5305013354：5305013354,5311434735,5311434737",
                "5305534945：5305534945,5311434743,5311434745",
                "5305534945：5305534945,5311434743,5311434745",
                "5305676687：5305676687,5311434747,5311434749,5311434751",
                "5305676687：5305676687,5311434747,5311434749,5311434751",
                "5305676687：5305676687,5311434747,5311434749,5311434751",
                "5307417095：5307417095,5311434753,5311434755",
                "5307417095：5307417095,5311434753,5311434755",
                "5307458349：5307458349,5311434757,5311434759",
                "5307458349：5307458349,5311434757,5311434759",
                "5307486021：5307486021,5311434761",
                "5307487148：5307487148,5311434763,5311434765",
                "5307487148：5307487148,5311434763,5311434765",
                "5308861409：5308861409,5311434771",
                "5308996231：5308996231,5311434773",
                "5309206114：5309206114,5311434775,5311434777,5311434779,5311434781",
                "5309206114：5309206114,5311434775,5311434777,5311434779,5311434781",
                "5309206114：5309206114,5311434775,5311434777,5311434779,5311434781",
                "5309206114：5309206114,5311434775,5311434777,5311434779,5311434781",
                "5309207368：5309207368,5311434783,5311434785",
                "5309207368：5309207368,5311434783,5311434785",
                "5309582786：5309582786,5311432395",
                "5309582786：5309582786,5311432395",
                "5298042265：5298042265,5311432395",
                "6149979393：5311432102,6149979393",
                "6149979393：5311432102,6149979393"
        );
        List<String> collect = list.stream().distinct().collect(Collectors.toList());
        System.out.println(JSON.toJSONString(collect));
    }

    @Test
    public void test2() throws IOException {
        List<String> strings = Arrays.asList("ga0CVdnvDtf", "GGHsms2Zmwe", "7x1Ik5bOaMk", "hkTCdOmlwVb", "hdE677Nadzw", "a4cM8GytvuM", "Ejv5A36HbBe", "d8rDavdqC0K", "u7YmrK1s544", "zHpeHLSsyJt", "7O7B5tiJ3Q9", "hzJ0pYnnnNc", "VwgiT4MilKy", "lpBYwOCpi1U", "aUeKvkMQCBW", "gVjffOk3qMB", "xlZkHgI1B2I", "KGtaqUU8N3m", "jaDJ7wxbq8r", "njLU5OmNj4v", "Ff4qEyOG3rL", "5AXWooS8t8f", "MmUtpmrCBvv", "DQVG7K4XrtK", "kUeipsyRcXZ", "DT0Ks291lgw", "vx5GrcVolRG", "WY0nG21F7u8", "FaLMmg3bnfb", "FvffORCZRLm", "gDwu2Yr5sne", "DSOMGIIASJR", "A9M7630ZnL7", "0YHmju5LwYj", "nY5IQIlAcOb", "lHt3Gc4waZG", "Q4n0rhQw0Y5", "yrB4GEsl58F", "dQtX34pjAW9", "fQlL2H28PdQ", "PLEANU1f29M", "pNh3TnXpg9b", "BotwuqjNQUS", "n5ZOrPwKMEx", "aJiFcNqqhey", "JVT9PXb5FtB", "FSLzBs7DwAY", "ANRWSzQxsJr", "WvOAMeKIPBq", "NhfFm0vbgYs", "0hlh4Snkzyf", "KSiCQr3ngZk");
        String longString = Files.lines(Paths.get("C:/Users/<USER>/Desktop/111.txt")) // 将 "path/to/your/file.txt" 替换为你的文件路径
                .collect(Collectors.joining(System.lineSeparator()));

        List<String> missingStrings = findMissingStrings(strings, longString);
        System.out.println(missingStrings);
    }

    /**
     * 找出列表中不在字符串中出现的元素。
     *
     * @param list  字符串列表
     * @param text  用于查找的字符串
     * @return  不在字符串中出现的元素列表
     */
    public static List<String> findMissingStrings(List<String> list, String text) {
        List<String> missingStrings = new ArrayList<>();

        for (String str : list) {
            if (!text.contains(str)) {
                missingStrings.add(str);
            }
        }

        return missingStrings;
    }

}