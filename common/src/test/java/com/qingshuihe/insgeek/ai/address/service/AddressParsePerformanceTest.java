package com.qingshuihe.insgeek.ai.address.service;

import com.qingshuihe.insgeek.ai.address.dto.PersonDto;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Random;

/**
 * 地址解析性能基准测试
 */
@SpringBootTest
public class AddressParsePerformanceTest {

    @Autowired
    private AddressParseService addressParseService;

    /**
     * 详细的性能基准测试
     */
    @Test
    public void performanceDetailedTest() {
        System.out.println("=== 地址解析性能基准测试 ===");
        
        // 测试不同数据量的性能
        int[] testSizes = {100, 500, 1000, 2000, 5000};
        
        for (int size : testSizes) {
            testPerformanceWithSize(size);
        }
        
        // 测试复杂地址的性能
        testComplexAddressPerformance();
    }

    /**
     * 测试指定数量的地址解析性能
     */
    private void testPerformanceWithSize(int size) {
        List<String> addresses = generateTestAddresses(size);
        
        // 预热JVM
        for (int i = 0; i < 3; i++) {
            addressParseService.parseAddressStrings(addresses.subList(0, Math.min(100, size)));
        }
        
        // 正式测试
        long startTime = System.nanoTime();
        List<PersonDto> results = addressParseService.parseAddressStrings(addresses);
        long endTime = System.nanoTime();
        
        long durationMs = (endTime - startTime) / 1_000_000;
        double avgTimePerAddress = (double) durationMs / size;
        
        System.out.printf("数据量: %d, 总耗时: %d ms, 平均每条: %.2f ms, 成功解析: %d\n", 
                size, durationMs, avgTimePerAddress, countSuccessfulParses(results));
        
        // 验证性能要求
        if (size == 5000) {
            if (durationMs <= 3000) {
                System.out.println("✓ 5000条数据解析性能测试通过！");
            } else {
                System.out.println("✗ 5000条数据解析性能测试失败，耗时超过3秒");
            }
        }
    }

    /**
     * 测试复杂地址的解析性能
     */
    private void testComplexAddressPerformance() {
        System.out.println("\n=== 复杂地址解析测试 ===");
        
        List<String> complexAddresses = Arrays.asList(
                "深圳园，北京小红门乡七号楼单元2朝阳区二03",
                "深圳园北京市天津市上海市",
                "广东省深圳市南山区科技园北京大学深圳研究院",
                "从北京市朝阳区到上海市浦东新区再到深圳市南山区",
                "杭州西湖区文三路靠近上海路与北京路交叉口",
                "成都市锦江区春熙路IFS国际金融中心重庆小面店",
                "武汉市武昌区中南路湖北省人民医院附近",
                "南京市鼓楼区中山路新街口商圈苏州银行",
                "天津市和平区南京路步行街北京烤鸭店",
                "重庆市渝中区解放碑步行街成都火锅店"
        );
        
        // 扩展到1000条复杂地址
        List<String> addresses = new ArrayList<>();
        for (int i = 0; i < 1000; i++) {
            addresses.add(complexAddresses.get(i % complexAddresses.size()) + "_" + i);
        }
        
        long startTime = System.nanoTime();
        List<PersonDto> results = addressParseService.parseAddressStrings(addresses);
        long endTime = System.nanoTime();
        
        long durationMs = (endTime - startTime) / 1_000_000;
        
        System.out.printf("复杂地址解析 - 数据量: %d, 耗时: %d ms, 成功解析: %d\n", 
                addresses.size(), durationMs, countSuccessfulParses(results));
        
        // 分析解析结果
        analyzeParseResults(results, complexAddresses);
    }

    /**
     * 生成测试地址数据
     */
    private List<String> generateTestAddresses(int size) {
        String[] baseAddresses = {
                "深圳市南山区科技园",
                "北京市朝阳区国贸",
                "上海市浦东新区陆家嘴",
                "广州市天河区珠江新城",
                "杭州市西湖区文三路",
                "成都市锦江区春熙路",
                "武汉市武昌区中南路",
                "南京市鼓楼区中山路",
                "天津市和平区南京路",
                "重庆市渝中区解放碑",
                "西安市雁塔区高新区",
                "苏州市工业园区",
                "无锡市滨湖区",
                "宁波市鄞州区",
                "青岛市市南区",
                "大连市中山区",
                "厦门市思明区",
                "福州市鼓楼区",
                "长沙市岳麓区",
                "郑州市金水区"
        };
        
        List<String> addresses = new ArrayList<>();
        Random random = new Random(42); // 固定种子确保可重复性
        
        for (int i = 0; i < size; i++) {
            String baseAddress = baseAddresses[i % baseAddresses.length];
            
            // 随机添加一些变化
            if (random.nextBoolean()) {
                baseAddress += "第" + (random.nextInt(100) + 1) + "号";
            }
            if (random.nextBoolean()) {
                baseAddress += "大厦" + (random.nextInt(50) + 1) + "层";
            }
            
            addresses.add(baseAddress);
        }
        
        return addresses;
    }

    /**
     * 统计成功解析的数量
     */
    private int countSuccessfulParses(List<PersonDto> results) {
        return (int) results.stream()
                .filter(person -> person.getMedicareProv() != null && person.getMedicareCity() != null)
                .count();
    }

    /**
     * 分析解析结果
     */
    private void analyzeParseResults(List<PersonDto> results, List<String> originalAddresses) {
        System.out.println("\n=== 解析结果分析 ===");
        
        for (int i = 0; i < Math.min(originalAddresses.size(), results.size()); i++) {
            PersonDto result = results.get(i);
            String original = originalAddresses.get(i);
            
            if (result.getMedicareProv() != null && result.getMedicareCity() != null) {
                System.out.printf("原地址: %s\n解析结果: %s - %s\n\n", 
                        original, result.getMedicareProv(), result.getMedicareCity());
            } else {
                System.out.printf("原地址: %s\n解析结果: 未识别\n\n", original);
            }
        }
    }

    /**
     * 内存使用情况测试
     */
    @Test
    public void memoryUsageTest() {
        System.out.println("=== 内存使用情况测试 ===");
        
        Runtime runtime = Runtime.getRuntime();
        
        // 强制垃圾回收
        System.gc();
        long beforeMemory = runtime.totalMemory() - runtime.freeMemory();
        
        // 执行大量地址解析
        List<String> addresses = generateTestAddresses(10000);
        List<PersonDto> results = addressParseService.parseAddressStrings(addresses);
        
        long afterMemory = runtime.totalMemory() - runtime.freeMemory();
        long memoryUsed = afterMemory - beforeMemory;
        
        System.out.printf("解析10000条地址的内存使用: %.2f MB\n", memoryUsed / 1024.0 / 1024.0);
        System.out.printf("平均每条地址内存使用: %.2f KB\n", memoryUsed / 1024.0 / addresses.size());
        
        // 清理引用
        addresses = null;
        results = null;
        System.gc();
    }
}
