package com.qingshuihe.insgeek.ai.address.service;

import com.qingshuihe.insgeek.ai.address.dto.PersonDto;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Comparator;
import java.util.List;

/**
 * 地址解析调试测试
 */
@SpringBootTest
public class AddressParseDebugTest {

    @Autowired
    private AddressParseService addressParseService;
    
    @Autowired
    private AddressMappingCache addressMappingCache;

    /**
     * 测试重叠匹配的处理
     */
    @Test
    public void testOverlapHandling() {
        System.out.println("=== 测试重叠匹配处理 ===");

        // 测试"深圳园北京市"应该选择"北京市"而不是"深圳"
        String testAddress = "深圳园北京市";
        List<PersonDto> results = addressParseService.parseAddressStrings(Arrays.asList(testAddress));
        PersonDto result = results.get(0);

        System.out.printf("地址: %s\n", testAddress);
        System.out.printf("解析结果: 省=%s, 市=%s\n", result.getMedicareProv(), result.getMedicareCity());

        // 根据用户需求，应该选择"北京市"（更长的匹配）
        if ("北京".equals(result.getMedicareProv()) && "北京市".equals(result.getMedicareCity())) {
            System.out.println("✅ 正确：选择了北京市");
        } else {
            System.out.println("❌ 错误：应该选择北京市，但选择了" + result.getMedicareCity());
            System.out.println("需要修复算法逻辑");
        }
    }

    /**
     * 调试具体的地址解析问题
     */
    @Test
    public void debugSpecificAddresses() {
        System.out.println("=== 调试地址解析问题 ===");

        // 测试具体的问题地址
        String[] testAddresses = {
                "深圳园，北京小红门乡七号楼单元2朝阳区二03",
                "深圳园北京市",
                "深圳北京元",
                "深北元静",
                "深圳市",
                "深圳",
                "北京市"
        };

        for (String address : testAddresses) {
            System.out.println("\n--- 测试地址: " + address + " ---");

            // 测试前缀树匹配
            List<TrieTree.CityMatchResult> matches = addressMappingCache.findCityMatchesWithPosition(address);
            System.out.println("前缀树匹配结果: " + matches);

            // 测试selectBestMatch逻辑
            if (!matches.isEmpty()) {
                // 模拟selectBestMatch的逻辑
                List<TrieTree.CityMatchResult> copyMatches = new ArrayList<>(matches);
                copyMatches.sort(Comparator.comparingInt(TrieTree.CityMatchResult::getStartPosition));

                System.out.println("排序后的匹配: " + copyMatches);

                // 检查重叠处理
                for (int i = 0; i < copyMatches.size() - 1; i++) {
                    TrieTree.CityMatchResult current = copyMatches.get(i);
                    TrieTree.CityMatchResult next = copyMatches.get(i + 1);

                    if (next.getStartPosition() <= current.getEndPosition()) {
                        System.out.printf("发现重叠: %s (位置%d-%d) 与 %s (位置%d-%d)\n",
                                current.getCityName(), current.getStartPosition(), current.getEndPosition(),
                                next.getCityName(), next.getStartPosition(), next.getEndPosition());

                        if (current.getCityName().length() >= next.getCityName().length()) {
                            System.out.println("选择: " + current.getCityName() + " (更长或相等)");
                        } else {
                            System.out.println("选择: " + next.getCityName() + " (更长)");
                        }
                    }
                }

                System.out.println("最终选择的第一个: " + copyMatches.get(0).getCityName());
            }

            // 测试完整解析
            List<PersonDto> results = addressParseService.parseAddressStrings(Arrays.asList(address));
            PersonDto result = results.get(0);

            System.out.printf("解析结果: 省=%s, 市=%s\n",
                    result.getMedicareProv(), result.getMedicareCity());

            // 验证特定的期望结果
            if (address.equals("深圳园北京市")) {
                if ("北京".equals(result.getMedicareProv()) && "北京市".equals(result.getMedicareCity())) {
                    System.out.println("✅ 正确：选择了北京市（更长的匹配）");
                } else {
                    System.out.println("❌ 错误：应该选择北京市，但选择了" + result.getMedicareCity());
                }
            } else if (address.equals("深圳市")) {
                if ("广东省".equals(result.getMedicareProv()) && "深圳市".equals(result.getMedicareCity())) {
                    System.out.println("✅ 正确：选择了深圳市（更长的匹配）");
                } else {
                    System.out.println("❌ 错误：应该选择深圳市，但选择了" + result.getMedicareCity());
                }
            }
        }
    }

    /**
     * 测试地址数据是否正确加载
     */
    @Test
    public void testAddressDataLoading() {
        System.out.println("=== 测试地址数据加载 ===");
        
        // 测试一些已知的城市
        String[] knownCities = {"深圳市", "北京市", "上海市", "广州市"};
        
        for (String city : knownCities) {
            boolean isValid = addressMappingCache.isValidCity(city);
            System.out.printf("城市 %s 是否有效: %s\n", city, isValid);
            
            if (isValid) {
                com.qingshuihe.insgeek.ai.address.dto.AddressInfo addressInfo = addressMappingCache.getAddressInfo(city);
                System.out.printf("  省: %s, 市: %s, 代码: %d\n",
                        addressInfo.getProv(), addressInfo.getCity(), addressInfo.getCode());
            }
        }
    }

    /**
     * 测试前缀树的基本功能
     */
    @Test
    public void testTrieTreeBasics() {
        System.out.println("=== 测试前缀树基本功能 ===");
        
        String testText = "深圳园北京市";
        System.out.println("测试文本: " + testText);
        
        // 测试逐字符匹配
        for (int i = 0; i < testText.length(); i++) {
            for (int j = i + 1; j <= testText.length(); j++) {
                String substring = testText.substring(i, j);
                boolean isValid = addressMappingCache.isValidCity(substring);
                if (isValid) {
                    System.out.printf("找到有效城市: %s (位置 %d-%d)\n", substring, i, j-1);
                }
            }
        }
    }
}
