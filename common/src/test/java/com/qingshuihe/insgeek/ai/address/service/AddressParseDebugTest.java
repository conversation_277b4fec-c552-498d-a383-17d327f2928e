package com.qingshuihe.insgeek.ai.address.service;

import com.qingshuihe.insgeek.ai.address.dto.PersonDto;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

import java.util.Arrays;
import java.util.List;

/**
 * 地址解析调试测试
 */
@SpringBootTest
public class AddressParseDebugTest {

    @Autowired
    private AddressParseService addressParseService;
    
    @Autowired
    private AddressMappingCache addressMappingCache;

    /**
     * 调试具体的地址解析问题
     */
    @Test
    public void debugSpecificAddresses() {
        System.out.println("=== 调试地址解析问题 ===");
        
        // 测试具体的问题地址
        String[] testAddresses = {
                "深圳园，北京小红门乡七号楼单元2朝阳区二03",
                "深圳园北京市",
                "深圳北京元",
                "深北元静",
                "深圳市",
                "深圳",
                "北京市"
        };
        
        for (String address : testAddresses) {
            System.out.println("\n--- 测试地址: " + address + " ---");
            
            // 测试前缀树匹配
            List<TrieTree.CityMatchResult> matches = addressMappingCache.findCityMatchesWithPosition(address);
            System.out.println("前缀树匹配结果: " + matches);
            
            // 测试完整解析
            List<PersonDto> results = addressParseService.parseAddressStrings(Arrays.asList(address));
            PersonDto result = results.get(0);
            
            System.out.printf("解析结果: 省=%s, 市=%s\n", 
                    result.getMedicareProv(), result.getMedicareCity());
        }
    }

    /**
     * 测试地址数据是否正确加载
     */
    @Test
    public void testAddressDataLoading() {
        System.out.println("=== 测试地址数据加载 ===");
        
        // 测试一些已知的城市
        String[] knownCities = {"深圳市", "北京市", "上海市", "广州市"};
        
        for (String city : knownCities) {
            boolean isValid = addressMappingCache.isValidCity(city);
            System.out.printf("城市 %s 是否有效: %s\n", city, isValid);
            
            if (isValid) {
                com.qingshuihe.insgeek.ai.address.dto.AddressInfo addressInfo = addressMappingCache.getAddressInfo(city);
                System.out.printf("  省: %s, 市: %s, 代码: %d\n",
                        addressInfo.getProv(), addressInfo.getCity(), addressInfo.getCode());
            }
        }
    }

    /**
     * 测试前缀树的基本功能
     */
    @Test
    public void testTrieTreeBasics() {
        System.out.println("=== 测试前缀树基本功能 ===");
        
        String testText = "深圳园北京市";
        System.out.println("测试文本: " + testText);
        
        // 测试逐字符匹配
        for (int i = 0; i < testText.length(); i++) {
            for (int j = i + 1; j <= testText.length(); j++) {
                String substring = testText.substring(i, j);
                boolean isValid = addressMappingCache.isValidCity(substring);
                if (isValid) {
                    System.out.printf("找到有效城市: %s (位置 %d-%d)\n", substring, i, j-1);
                }
            }
        }
    }
}
