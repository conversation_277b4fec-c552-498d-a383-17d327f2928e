# 开发环境配置
spring:
  # 数据库配置 - 开发环境
  datasource:
    driver-class-name: com.mysql.cj.jdbc.Driver
    url: ***********************************************************************************************************************
    username: qingshuihe
    password: qingshuihe
    # 连接池配置
    hikari:
      maximum-pool-size: 10
      minimum-idle: 5
      connection-timeout: 30000
      idle-timeout: 600000
      max-lifetime: 1800000

  # Kafka配置 - 开发环境
  kafka:
    bootstrap-servers: 127.0.0.1:9092
    template:
      default-topic: qingshuihe_dev
    producer:
      key-serializer: org.apache.kafka.common.serialization.StringSerializer
      value-serializer: org.apache.kafka.common.serialization.StringSerializer
      # 开发环境可以设置较小的批次大小以便调试
      batch-size: 1024
    consumer:
      group-id: qingshuiheGroup_dev
      enable-auto-commit: true
      auto-commit-interval: 1000
      key-deserializer: org.apache.kafka.common.serialization.StringDeserializer
      value-deserializer: org.apache.kafka.common.serialization.StringDeserializer
    properties:
      security:
        protocol: SASL_PLAINTEXT
      sasl:
        mechanism: PLAIN
        jaas:
          config: 'org.apache.kafka.common.security.scram.ScramLoginModule required username="qingshuihe" password="qingshuihe";'

  # 邮件配置 - 开发环境
  mail:
    username: <EMAIL>
    password: aexinfxhiusobdcd
    host: smtp.qq.com
    properties:
      mail:
        smtp:
          ssl: true
          debug: true  # 开发环境开启邮件调试

# Redis配置 - 开发环境
redis-config:
  pool:
    password: 
    host: 127.0.0.1
    port: 6379
    # 开发环境连接池配置（较小）
    maxTotal: 20
    maxIdle: 8
    minIdle: 2
    maxWaitMillis: 10000
    softMinEvictableIdleTimeMillis: 10000
    testOnBorrow: true
    testOnReturn: true
    testWhileIdle: true
    timeBetweenEvictionRunsMillis: 30000
    minEvictableIdleTimeMillis: 1800000
    numTestsPerEvictionRun: 3
    blockWhenExhausted: true
    jmxEnabled: true

# 日志配置 - 开发环境
logging:
  level:
    com.qingshuihe: DEBUG
    org.springframework.security: DEBUG
    org.springframework.web: DEBUG
    org.mybatis: DEBUG
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{36} - %msg%n"

# 开发环境特定配置
debug: true

# 文件上传路径 - 开发环境
file:
  upload:
    hostPath: D:\uploadFiles\dev
