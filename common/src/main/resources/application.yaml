# 主配置文件 - 通用配置和环境选择
spring:
  profiles:
    # 默认激活开发环境，可通过启动参数覆盖：--spring.profiles.active=fat
    active: dev
  config:
    # 明确指定配置文件位置，避免搜索 config/*/ 目录
    location: classpath:/application.yaml
  
  # 通用配置 - 所有环境共享
  servlet:
    multipart:
      max-file-size: 1024MB
      max-request-size: 1024MB

# MyBatis Plus 通用配置
mybatis-plus:
  configuration:
    # 设置打印sql信息到控制台（生产环境可关闭）
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl
  # 设置逻辑删除
  global-config:
    db-config:
      logic-delete-field: enable
      logic-delete-value: 1

# 文件上传通用配置
file:
  upload:
    hostPath: D:\uploadFiles

# 重置密码邮件通用配置
resetpw:
  subject: 重置密码
  html: <div><br></div><div>尊敬的REST_USER_NAME，你好</div><div>&nbsp; &nbsp; &nbsp; 你正在进行重置密码操作，如为本人操作，请点击<a href="FRONT_PAGE_SETPW">此链接</a>进行重置密码操作；如非本人操作，请忽略。链接VAILD_TIME分钟内有效。</div><div>
  frontPage: https://qingshuihe.com/initpw/
  vaildTime: 10
  text: 本次验证码为：VERIFY_CODE，VAILD_TIME分钟内有效

# 应用信息
info:
  app:
    name: qingshuihe
    description: 清水河项目
    version: 1.0.0

# 管理端点配置
management:
  endpoints:
    web:
      exposure:
        include: health,info,env,configprops
  endpoint:
    health:
      show-details: when-authorized
