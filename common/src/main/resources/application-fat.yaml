# 功能测试环境配置 (FAT - Function Acceptance Test)
spring:
  # 数据库配置 - 测试环境
  datasource:
    driver-class-name: com.mysql.cj.jdbc.Driver
    url: *********************************************************************************************************************************
    username: qingshuihe_test
    password: ${DB_PASSWORD:qingshuihe_test123}  # 支持环境变量
    # 连接池配置 - 测试环境
    hikari:
      maximum-pool-size: 15
      minimum-idle: 5
      connection-timeout: 30000
      idle-timeout: 600000
      max-lifetime: 1800000

  # Kafka配置 - 测试环境
  kafka:
    bootstrap-servers: test-kafka-server:9092
    template:
      default-topic: qingshuihe_test
    producer:
      key-serializer: org.apache.kafka.common.serialization.StringSerializer
      value-serializer: org.apache.kafka.common.serialization.StringSerializer
      batch-size: 4096
      retries: 3
    consumer:
      group-id: qingshuiheGroup_test
      enable-auto-commit: true
      auto-commit-interval: 1000
      key-deserializer: org.apache.kafka.common.serialization.StringDeserializer
      value-deserializer: org.apache.kafka.common.serialization.StringDeserializer
    properties:
      security:
        protocol: SASL_PLAINTEXT
      sasl:
        mechanism: PLAIN
        jaas:
          config: 'org.apache.kafka.common.security.scram.ScramLoginModule required username="qingshuihe_test" password="${KAFKA_PASSWORD:qingshuihe_test}";'

  # 邮件配置 - 测试环境
  mail:
    username: <EMAIL>
    password: ${MAIL_PASSWORD:test_password}
    host: smtp.qingshuihe.com
    properties:
      mail:
        smtp:
          ssl: true
          debug: false

# Redis配置 - 测试环境
redis-config:
  pool:
    password: ${REDIS_PASSWORD:}
    host: test-redis-server
    port: 6379
    # 测试环境连接池配置
    maxTotal: 50
    maxIdle: 15
    minIdle: 5
    maxWaitMillis: 10000
    softMinEvictableIdleTimeMillis: 10000
    testOnBorrow: true
    testOnReturn: true
    testWhileIdle: true
    timeBetweenEvictionRunsMillis: 30000
    minEvictableIdleTimeMillis: 1800000
    numTestsPerEvictionRun: 3
    blockWhenExhausted: true
    jmxEnabled: true

# 日志配置 - 测试环境
logging:
  level:
    com.qingshuihe: INFO
    org.springframework.security: WARN
    org.springframework.web: WARN
    org.mybatis: INFO
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{36} - %msg%n"

# MyBatis Plus 测试环境配置
mybatis-plus:
  configuration:
    # 测试环境关闭SQL打印
    log-impl: org.apache.ibatis.logging.nop.NopImpl
  # 设置逻辑删除
  global-config:
    db-config:
      logic-delete-field: enable
      logic-delete-value: 1

# 文件上传路径 - 测试环境
file:
  upload:
    hostPath: /data/uploadFiles/test

# 重置密码邮件配置 - 测试环境
resetpw:
  frontPage: https://test.qingshuihe.com/initpw/
