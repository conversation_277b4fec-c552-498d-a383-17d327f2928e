# 用户验收测试环境配置 (UAT - User Acceptance Test)
spring:
  # 数据库配置 - UAT环境
  datasource:
    driver-class-name: com.mysql.cj.jdbc.Driver
    url: ******************************************************************************************************************************
    username: qingshuihe_uat
    password: ${DB_PASSWORD:qingshuihe_uat456}
    # 连接池配置 - UAT环境（接近生产）
    hikari:
      maximum-pool-size: 30
      minimum-idle: 10
      connection-timeout: 30000
      idle-timeout: 600000
      max-lifetime: 1800000
      leak-detection-threshold: 60000  # 连接泄漏检测

  # Kafka配置 - UAT环境
  kafka:
    bootstrap-servers: uat-kafka-server:9092
    template:
      default-topic: qingshuihe_uat
    producer:
      key-serializer: org.apache.kafka.common.serialization.StringSerializer
      value-serializer: org.apache.kafka.common.serialization.StringSerializer
      batch-size: 8192
      retries: 5
      acks: all  # 确保消息可靠性
    consumer:
      group-id: qingshuiheGroup_uat
      enable-auto-commit: false  # 手动提交，确保消息处理可靠性
      auto-offset-reset: earliest
      key-deserializer: org.apache.kafka.common.serialization.StringDeserializer
      value-deserializer: org.apache.kafka.common.serialization.StringDeserializer
    properties:
      security:
        protocol: SASL_SSL
      sasl:
        mechanism: PLAIN
        jaas:
          config: 'org.apache.kafka.common.security.scram.ScramLoginModule required username="qingshuihe_uat" password="${KAFKA_PASSWORD:qingshuihe_uat}";'

  # 邮件配置 - UAT环境
  mail:
    username: <EMAIL>
    password: ${MAIL_PASSWORD:uat_password}
    host: smtp.qingshuihe.com
    port: 587
    properties:
      mail:
        smtp:
          ssl: true
          auth: true
          starttls:
            enable: true

# Redis配置 - UAT环境
redis-config:
  pool:
    password: ${REDIS_PASSWORD:uat_redis_password}
    host: uat-redis-server
    port: 6379
    # UAT环境连接池配置（较大）
    maxTotal: 100
    maxIdle: 20
    minIdle: 10
    maxWaitMillis: 5000
    softMinEvictableIdleTimeMillis: 10000
    testOnBorrow: true
    testOnReturn: true
    testWhileIdle: true
    timeBetweenEvictionRunsMillis: 30000
    minEvictableIdleTimeMillis: 1800000
    numTestsPerEvictionRun: 5
    blockWhenExhausted: true
    jmxEnabled: true

# 日志配置 - UAT环境
logging:
  level:
    com.qingshuihe: INFO
    org.springframework.security: WARN
    org.springframework.web: WARN
    org.mybatis: WARN
    org.springframework.kafka: INFO
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{36} - %msg%n"
    file: "%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{36} - %msg%n"
  file:
    name: /var/log/qingshuihe/application-uat.log

# MyBatis Plus UAT环境配置
mybatis-plus:
  configuration:
    # UAT环境关闭SQL打印
    log-impl: org.apache.ibatis.logging.nop.NopImpl
  # 设置逻辑删除
  global-config:
    db-config:
      logic-delete-field: enable
      logic-delete-value: 1

# 文件上传路径 - UAT环境
file:
  upload:
    hostPath: /data/uploadFiles/uat

# 重置密码邮件配置 - UAT环境
resetpw:
  frontPage: https://uat.qingshuihe.com/initpw/

# 监控配置 - UAT环境
management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics,prometheus
  endpoint:
    health:
      show-details: always
  metrics:
    export:
      prometheus:
        enabled: true
