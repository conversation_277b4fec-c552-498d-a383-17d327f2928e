# 生产环境配置 (Production)
spring:
  # 数据库配置 - 生产环境
  datasource:
    driver-class-name: com.mysql.cj.jdbc.Driver
    url: *****************************************************************************************************************************************************************************
    username: ${DB_USERNAME:qingshuihe_prod}
    password: ${DB_PASSWORD}  # 生产环境必须使用环境变量
    # 生产环境连接池配置（高性能）
    hikari:
      maximum-pool-size: 50
      minimum-idle: 20
      connection-timeout: 30000
      idle-timeout: 600000
      max-lifetime: 1800000
      leak-detection-threshold: 60000
      connection-test-query: SELECT 1

  # Kafka配置 - 生产环境
  kafka:
    bootstrap-servers: ${KAFKA_SERVERS:prod-kafka-cluster:9092}
    template:
      default-topic: qingshuihe_prod
    producer:
      key-serializer: org.apache.kafka.common.serialization.StringSerializer
      value-serializer: org.apache.kafka.common.serialization.StringSerializer
      batch-size: 16384
      retries: 10
      acks: all
      compression-type: lz4  # 生产环境启用压缩
      buffer-memory: 33554432
    consumer:
      group-id: qingshuiheGroup_prod
      enable-auto-commit: false
      auto-offset-reset: earliest
      max-poll-records: 500
      key-deserializer: org.apache.kafka.common.serialization.StringDeserializer
      value-deserializer: org.apache.kafka.common.serialization.StringDeserializer
    properties:
      security:
        protocol: SASL_SSL
      sasl:
        mechanism: PLAIN
        jaas:
          config: 'org.apache.kafka.common.security.scram.ScramLoginModule required username="${KAFKA_USERNAME}" password="${KAFKA_PASSWORD}";'

  # 邮件配置 - 生产环境
  mail:
    username: ${MAIL_USERNAME:<EMAIL>}
    password: ${MAIL_PASSWORD}
    host: ${MAIL_HOST:smtp.qingshuihe.com}
    port: 587
    properties:
      mail:
        smtp:
          ssl: true
          auth: true
          starttls:
            enable: true
          timeout: 25000
          connectiontimeout: 25000

# Redis配置 - 生产环境
redis-config:
  pool:
    password: ${REDIS_PASSWORD}
    host: ${REDIS_HOST:prod-redis-cluster}
    port: ${REDIS_PORT:6379}
    # 生产环境连接池配置（高并发）
    maxTotal: 200
    maxIdle: 50
    minIdle: 20
    maxWaitMillis: 3000
    softMinEvictableIdleTimeMillis: 10000
    testOnBorrow: false  # 生产环境关闭以提高性能
    testOnReturn: false
    testWhileIdle: true
    timeBetweenEvictionRunsMillis: 30000
    minEvictableIdleTimeMillis: 1800000
    numTestsPerEvictionRun: 10
    blockWhenExhausted: true
    jmxEnabled: false  # 生产环境关闭JMX

# 日志配置 - 生产环境
logging:
  level:
    com.qingshuihe: INFO
    org.springframework.security: WARN
    org.springframework.web: WARN
    org.mybatis: WARN
    org.springframework.kafka: WARN
    org.apache.kafka: WARN
    org.hibernate: WARN
    com.zaxxer.hikari: WARN
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{36} - %msg%n"
    file: "%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{36} - %msg%n"
  file:
    name: /var/log/qingshuihe/application.log
    max-size: 100MB
    max-history: 30

# MyBatis Plus 生产环境配置
mybatis-plus:
  configuration:
    # 生产环境关闭SQL打印
    log-impl: org.apache.ibatis.logging.nop.NopImpl
    # 开启二级缓存
    cache-enabled: true
  # 设置逻辑删除
  global-config:
    db-config:
      logic-delete-field: enable
      logic-delete-value: 1

# 文件上传路径 - 生产环境
file:
  upload:
    hostPath: ${FILE_UPLOAD_PATH:/data/uploadFiles/prod}

# 重置密码邮件配置 - 生产环境
resetpw:
  frontPage: https://www.qingshuihe.com/initpw/

# 监控配置 - 生产环境
management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics,prometheus
      base-path: /actuator
  endpoint:
    health:
      show-details: never  # 生产环境不暴露详细信息
  metrics:
    export:
      prometheus:
        enabled: true
  server:
    port: 8082  # 管理端口与应用端口分离

# 安全配置 - 生产环境
server:
  port: ${SERVER_PORT:8080}
  servlet:
    context-path: /api
  tomcat:
    max-threads: 200
    min-spare-threads: 10
