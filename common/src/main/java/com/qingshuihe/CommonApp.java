package com.qingshuihe;

import com.qingshuihe.common.infrastructure.security.config.RedisPoolPropertiesConfig;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.ConfigurableApplicationContext;

/**
 * @Description:
 * @Author: shl
 * @Date: 2022/8/29
 **/
@SpringBootApplication
@EnableConfigurationProperties(RedisPoolPropertiesConfig.class)
public class CommonApp {
    public static void main(String[] args) {
        System.setProperty("server.port", "8080");
        // 明确指定配置文件位置，避免搜索 config/*/ 目录
        System.setProperty("spring.config.location", "classpath:/application.yaml");
        ConfigurableApplicationContext run = SpringApplication.run(CommonApp.class, args);
        System.out.println("begin start application=====================");
    }
}

