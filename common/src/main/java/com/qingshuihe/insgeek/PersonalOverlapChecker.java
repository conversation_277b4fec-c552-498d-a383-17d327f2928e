package com.qingshuihe.insgeek;

import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.List;

/**
 * @Description:
 * @Author: shl
 * @Date: 2024/7/29
 **/
public class PersonalOverlapChecker {
    public static void main(String[] args) {
        List<PersonalDTO> personalDTOS = Arrays.asList(
                new PersonalDTO("A001", LocalDateTime.of(2024, 1, 1, 10, 0), LocalDateTime.of(2024, 1, 1, 12, 0)),
                new PersonalDTO("A001", LocalDateTime.of(2024, 1, 1, 10, 0), LocalDateTime.of(2024, 1, 1, 12, 0)),
                new PersonalDTO("A002", LocalDateTime.of(2024, 1, 1, 14, 0), LocalDateTime.of(2024, 1, 1, 15, 0))
        );

        boolean hasOverlap = checkForOverlaps(personalDTOS);
        System.out.println("Has overlap: " + hasOverlap);
    }

    private static boolean checkForOverlaps(List<PersonalDTO> dtos) {
        return dtos.stream()
                .flatMap(dto1 -> dtos.stream()
                        .filter(dto2 -> !dto1.equals(dto2) && isOverlapping(dto1, dto2))
                )
                .findAny() // 找到任意一个重叠的情况
                .isPresent(); // 如果找到了，返回 true
    }

    private static boolean isOverlapping(PersonalDTO dto1, PersonalDTO dto2) {
        return dto1.getStartTime().isBefore(dto2.getEndTime()) && dto1.getEndTime().isAfter(dto2.getStartTime());
    }
}