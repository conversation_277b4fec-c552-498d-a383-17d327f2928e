package com.qingshuihe.insgeek;

import com.fasterxml.jackson.annotation.JsonProperty;

public class Entity {
    @JsonProperty("id")
    private long id;

    @JsonProperty("created_at")
    private String createdAt;

    @JsonProperty("updated_at")
    private String updatedAt;

    @JsonProperty("created_by")
    private int createdBy;

    @JsonProperty("updated_by")
    private int updatedBy;

    @JsonProperty("tenant_id")
    private int tenantId;

    @JsonProperty("add_type")
    private String addType;

    @JsonProperty("add_time")
    private String addTime;

    @JsonProperty("benefit")
    private String benefit;

    @JsonProperty("benefit_price")
    private Price benefitPrice;

    @JsonProperty("bonus_info")
    private String bonusInfo;

    @JsonProperty("cert_type")
    private String certType;

    @JsonProperty("change_count")
    private String changeCount;

    @JsonProperty("default_config")
    private long defaultConfig;

    @JsonProperty("eid")
    private long eid;

    @JsonProperty("elastic_lock_type")
    private String elasticLockType;

    @JsonProperty("endtime")
    private String endtime;

    @JsonProperty("group_bonus_price")
    private Price groupBonusPrice;

    @JsonProperty("group_id")
    private long groupId;

    @JsonProperty("id_number")
    private String idNumber;

    @JsonProperty("info")
    private String info;

    @JsonProperty("mobile")
    private String mobile;

    @JsonProperty("new_start_time")
    private String newStartTime;

    @JsonProperty("operation_time")
    private String operationTime;

    @JsonProperty("order_single_id")
    private int orderSingleId;

    @JsonProperty("person_pay_price")
    private Price personPayPrice;

    @JsonProperty("plan_config")
    private long planConfig;

    @JsonProperty("plan_status")
    private String planStatus;

    @JsonProperty("platform_bonus_price")
    private Price platformBonusPrice;

    @JsonProperty("realname")
    private String realname;

    @JsonProperty("real_price")
    private Price realPrice;

    @JsonProperty("refuse_count")
    private String refuseCount;

    @JsonProperty("remove_time")
    private String removeTime;

    @JsonProperty("sequence")
    private int sequence;

    @JsonProperty("starttime")
    private String starttime;

    @JsonProperty("status")
    private String status;

    @JsonProperty("subject_id")
    private long subjectId;

    @JsonProperty("type")
    private String type;

    @JsonProperty("uid")
    private long uid;

    @JsonProperty("delete_flg")
    private boolean deleteFlg;

    @JsonProperty("category")
    private int category;

    @JsonProperty("salary")
    private Price salary;

    @JsonProperty("person_num")
    private String personNum;

    @JsonProperty("job_category")
    private int jobCategory;

    @JsonProperty("cert_code")
    private String certCode;

    @JsonProperty("plan_name")
    private String planName;

    @JsonProperty("username")
    private String username;

    @JsonProperty("modified_end_time")
    private String modifiedEndTime;

    @JsonProperty("is_delete")
    private boolean isDelete;

    @JsonProperty("relation_tenant_id")
    private String relationTenantId;

    @JsonProperty("user_order_id")
    private long userOrderId;

    @JsonProperty("trace_id")
    private long traceId;

    // Getters and Setters
    // ...

    public long getId() {
        return id;
    }

    public void setId(long id) {
        this.id = id;
    }

    public String getCreatedAt() {
        return createdAt;
    }

    public void setCreatedAt(String createdAt) {
        this.createdAt = createdAt;
    }

    public String getUpdatedAt() {
        return updatedAt;
    }

    public void setUpdatedAt(String updatedAt) {
        this.updatedAt = updatedAt;
    }

    public int getCreatedBy() {
        return createdBy;
    }

    public void setCreatedBy(int createdBy) {
        this.createdBy = createdBy;
    }

    public int getUpdatedBy() {
        return updatedBy;
    }

    public void setUpdatedBy(int updatedBy) {
        this.updatedBy = updatedBy;
    }

    public int getTenantId() {
        return tenantId;
    }

    public void setTenantId(int tenantId) {
        this.tenantId = tenantId;
    }

    public String getAddType() {
        return addType;
    }

    public void setAddType(String addType) {
        this.addType = addType;
    }

    public String getAddTime() {
        return addTime;
    }

    public void setAddTime(String addTime) {
        this.addTime = addTime;
    }

    public String getBenefit() {
        return benefit;
    }

    public void setBenefit(String benefit) {
        this.benefit = benefit;
    }

    public Price getBenefitPrice() {
        return benefitPrice;
    }

    public void setBenefitPrice(Price benefitPrice) {
        this.benefitPrice = benefitPrice;
    }

    public String getBonusInfo() {
        return bonusInfo;
    }

    public void setBonusInfo(String bonusInfo) {
        this.bonusInfo = bonusInfo;
    }

    public String getCertType() {
        return certType;
    }

    public void setCertType(String certType) {
        this.certType = certType;
    }

    public String getChangeCount() {
        return changeCount;
    }

    public void setChangeCount(String changeCount) {
        this.changeCount = changeCount;
    }

    public long getDefaultConfig() {
        return defaultConfig;
    }

    public void setDefaultConfig(long defaultConfig) {
        this.defaultConfig = defaultConfig;
    }

    public long getEid() {
        return eid;
    }

    public void setEid(long eid) {
        this.eid = eid;
    }

    public String getElasticLockType() {
        return elasticLockType;
    }

    public void setElasticLockType(String elasticLockType) {
        this.elasticLockType = elasticLockType;
    }

    public String getEndtime() {
        return endtime;
    }

    public void setEndtime(String endtime) {
        this.endtime = endtime;
    }

    public Price getGroupBonusPrice() {
        return groupBonusPrice;
    }

    public void setGroupBonusPrice(Price groupBonusPrice) {
        this.groupBonusPrice = groupBonusPrice;
    }

    public long getGroupId() {
        return groupId;
    }

    public void setGroupId(long groupId) {
        this.groupId = groupId;
    }

    public String getIdNumber() {
        return idNumber;
    }

    public void setIdNumber(String idNumber) {
        this.idNumber = idNumber;
    }

    public String getInfo() {
        return info;
    }

    public void setInfo(String info) {
        this.info = info;
    }

    public String getMobile() {
        return mobile;
    }

    public void setMobile(String mobile) {
        this.mobile = mobile;
    }

    public String getNewStartTime() {
        return newStartTime;
    }

    public void setNewStartTime(String newStartTime) {
        this.newStartTime = newStartTime;
    }

    public String getOperationTime() {
        return operationTime;
    }

    public void setOperationTime(String operationTime) {
        this.operationTime = operationTime;
    }

    public int getOrderSingleId() {
        return orderSingleId;
    }

    public void setOrderSingleId(int orderSingleId) {
        this.orderSingleId = orderSingleId;
    }

    public Price getPersonPayPrice() {
        return personPayPrice;
    }

    public void setPersonPayPrice(Price personPayPrice) {
        this.personPayPrice = personPayPrice;
    }

    public long getPlanConfig() {
        return planConfig;
    }

    public void setPlanConfig(long planConfig) {
        this.planConfig = planConfig;
    }

    public String getPlanStatus() {
        return planStatus;
    }

    public void setPlanStatus(String planStatus) {
        this.planStatus = planStatus;
    }

    public Price getPlatformBonusPrice() {
        return platformBonusPrice;
    }

    public void setPlatformBonusPrice(Price platformBonusPrice) {
        this.platformBonusPrice = platformBonusPrice;
    }

    public String getRealname() {
        return realname;
    }

    public void setRealname(String realname) {
        this.realname = realname;
    }

    public Price getRealPrice() {
        return realPrice;
    }

    public void setRealPrice(Price realPrice) {
        this.realPrice = realPrice;
    }

    public String getRefuseCount() {
        return refuseCount;
    }

    public void setRefuseCount(String refuseCount) {
        this.refuseCount = refuseCount;
    }

    public String getRemoveTime() {
        return removeTime;
    }

    public void setRemoveTime(String removeTime) {
        this.removeTime = removeTime;
    }

    public int getSequence() {
        return sequence;
    }

    public void setSequence(int sequence) {
        this.sequence = sequence;
    }

    public String getStarttime() {
        return starttime;
    }

    public void setStarttime(String starttime) {
        this.starttime = starttime;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public long getSubjectId() {
        return subjectId;
    }

    public void setSubjectId(long subjectId) {
        this.subjectId = subjectId;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public long getUid() {
        return uid;
    }

    public void setUid(long uid) {
        this.uid = uid;
    }

    public boolean isDeleteFlg() {
        return deleteFlg;
    }

    public void setDeleteFlg(boolean deleteFlg) {
        this.deleteFlg = deleteFlg;
    }

    public int getCategory() {
        return category;
    }

    public void setCategory(int category) {
        this.category = category;
    }

    public Price getSalary() {
        return salary;
    }

    public void setSalary(Price salary) {
        this.salary = salary;
    }

    public String getPersonNum() {
        return personNum;
    }

    public void setPersonNum(String personNum) {
        this.personNum = personNum;
    }

    public int getJobCategory() {
        return jobCategory;
    }

    public void setJobCategory(int jobCategory) {
        this.jobCategory = jobCategory;
    }

    public String getCertCode() {
        return certCode;
    }

    public void setCertCode(String certCode) {
        this.certCode = certCode;
    }

    public String getPlanName() {
        return planName;
    }

    public void setPlanName(String planName) {
        this.planName = planName;
    }

    public String getUsername() {
        return username;
    }

    public void setUsername(String username) {
        this.username = username;
    }

    public String getModifiedEndTime() {
        return modifiedEndTime;
    }

    public void setModifiedEndTime(String modifiedEndTime) {
        this.modifiedEndTime = modifiedEndTime;
    }

    public boolean isDelete() {
        return isDelete;
    }

    public void setDelete(boolean isDelete) {
        this.isDelete = isDelete;
    }

    public String getRelationTenantId() {
        return relationTenantId;
    }

    public void setRelationTenantId(String relationTenantId) {
        this.relationTenantId = relationTenantId;
    }

    public long getUserOrderId() {
        return userOrderId;
    }

    public void setUserOrderId(long userOrderId) {
        this.userOrderId = userOrderId;
    }

    public long getTraceId() {
        return traceId;
    }

    public void setTraceId(long traceId) {
        this.traceId = traceId;
    }

    @Override
    public String toString() {
        return "Entity{" +
                "id=" + id +
                ", createdAt='" + createdAt + '\'' +
                ", updatedAt='" + updatedAt + '\'' +
                ", createdBy=" + createdBy +
                ", updatedBy=" + updatedBy +
                ", tenantId=" + tenantId +
                ", addType='" + addType + '\'' +
                ", addTime='" + addTime + '\'' +
                ", benefit='" + benefit + '\'' +
                ", benefitPrice=" + benefitPrice +
                ", bonusInfo='" + bonusInfo + '\'' +
                ", certType='" + certType + '\'' +
                ", changeCount='" + changeCount + '\'' +
                ", defaultConfig=" + defaultConfig +
                ", eid=" + eid +
                ", elasticLockType='" + elasticLockType + '\'' +
                ", endtime='" + endtime + '\'' +
                ", groupBonusPrice=" + groupBonusPrice +
                ", groupId=" + groupId +
                ", idNumber='" + idNumber + '\'' +
                ", info='" + info + '\'' +
                ", mobile='" + mobile + '\'' +
                ", newStartTime='" + newStartTime + '\'' +
                ", operationTime='" + operationTime + '\'' +
                ", orderSingleId=" + orderSingleId +
                ", personPayPrice=" + personPayPrice +
                ", planConfig=" + planConfig +
                ", planStatus='" + planStatus + '\'' +
                ", platformBonusPrice=" + platformBonusPrice +
                ", realname='" + realname + '\'' +
                ", realPrice=" + realPrice +
                ", refuseCount='" + refuseCount + '\'' +
                ", removeTime='" + removeTime + '\'' +
                ", sequence=" + sequence +
                ", starttime='" + starttime + '\'' +
                ", status='" + status + '\'' +
                ", subjectId=" + subjectId +
                ", type='" + type + '\'' +
                ", uid=" + uid +
                ", deleteFlg=" + deleteFlg +
                ", category=" + category +
                ", salary=" + salary +
                ", personNum='" + personNum + '\'' +
                ", jobCategory=" + jobCategory +
                ", certCode='" + certCode + '\'' +
                ", planName='" + planName + '\'' +
                ", username='" + username + '\'' +
                ", modifiedEndTime='" + modifiedEndTime + '\'' +
                ", isDelete=" + isDelete +
                ", relationTenantId='" + relationTenantId + '\'' +
                ", userOrderId=" + userOrderId +
                ", traceId=" + traceId +
                '}';
    }
}
