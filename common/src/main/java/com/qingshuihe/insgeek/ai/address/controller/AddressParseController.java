
package com.qingshuihe.insgeek.ai.address.controller;

import com.qingshuihe.insgeek.ai.address.dto.PersonDto;
import com.qingshuihe.insgeek.ai.address.service.AddressParseService;
import com.qingshuihe.common.interfaces.outbond.dto.ResultDo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.List;

@RestController
@RequestMapping("/api/address")
public class AddressParseController {

    @Autowired
    private AddressParseService addressParseService;
    /**
     * 解析人员地址信息
     *
     * @param addresses 待解析的人员地址列表
     * @return 解析结果，包含解析后的人员地址信息
     */
    @PostMapping("/parse/person")
    public ResultDo<List<PersonDto>> parsePerson(@RequestBody List<PersonDto> addresses) {
        addressParseService.parsePersonAddresses(addresses);
        ResultDo<List<PersonDto>> result = new ResultDo<>();
        result.setCode(200);
        result.setMessage("地址解析成功");
        result.setObj(addresses);
        return result;
    }
    /**
     * 解析地址字符串列表，将地址信息转换为人员信息对象列表
     *
     * @param addresses 待解析的地址字符串列表
     * @return 包含解析结果的统一返回对象，其中obj字段包含解析后的PersonDto列表
     */
    @PostMapping("/parse/address")
    public ResultDo<List<PersonDto>> parseAddresses(@RequestBody List<String> addresses) {
        List<PersonDto> results = addressParseService.parseAddressStrings(addresses);
        ResultDo<List<PersonDto>> result = new ResultDo<>();
        result.setCode(200);
        result.setMessage("地址解析成功");
        result.setObj(results);
        return result;
    }

    @GetMapping("/test-data")
    public ResultDo<List<PersonDto>> getTestData() {
        ResultDo<List<PersonDto>> result = new ResultDo<>();
        result.setCode(200);
        result.setMessage("获取测试数据成功");
        result.setObj(generateTestData());
        return result;
    }

    private List<PersonDto> generateTestData() {
        List<PersonDto> persons = new ArrayList<>();

        // 生成100条测试数据，地址分布在全国各地
        String[] addresses = {
                "北京市朝阳区小红门乡七号楼二单元203",
                "上海市浦东新区张江高科技园区",
                "广州市天河区珠江新城华夏路123号",
                "深圳市南山区科技园南区深南大道10000号",
                "杭州市西湖区文三路259号昌地火炬大厦",
                "成都市锦江区春熙路步行街",
                "武汉市江汉区解放大道690号武汉国际广场",
                "西安市雁塔区长安南路123号",
                "沈阳市和平区中华路123号",
                "大连市中山区人民路55号",
                "青岛市市南区香港中路123号",
                "厦门市思明区鹭江道123号",
                "南京市鼓楼区中山路123号",
                "苏州市姑苏区观前街123号",
                "重庆市渝中区解放碑步行街",
                "天津市和平区南京路123号",
                "长沙市岳麓区溁湾镇溁银大厦",
                "昆明市五华区金碧路123号",
                "郑州市金水区花园路123号",
                "石家庄市长安区中山东路123号",
                "太原市小店区亲贤街123号",
                "合肥市蜀山区长江西路123号",
                "福州市鼓楼区东街口123号",
                "南宁市青秀区民族大道123号",
                "济南市历下区泉城路123号",
                "长春市朝阳区人民大街123号",
                "哈尔滨市南岗区东大直街123号",
                "南昌市东湖区八一广场123号",
                "兰州市城关区张掖路123号",
                "呼和浩特色特区新城区新华大街123号",
                "银川市兴庆区解放西街123号",
                "西宁市城中区长江路123号",
                "乌鲁木齐市天山区新华南路123号",
                "拉萨市城关区北京中路123号",
                "海口市龙华区滨海大道123号",
                "贵阳市南明区中华南路123号",
                "南宁市西乡塘区大学东路123号",
                "昆明市盘龙区北京路123号",
                "福州市台江区五一中路123号",
                "泉州市丰泽区津淮街123号",
                "温州市鹿城区车站大道123号",
                "宁波市鄞州区中山东路123号",
                "无锡市梁溪区中山路123号",
                "常州市天宁区延陵西路123号",
                "大连市西岗区中山路123号",
                "青岛市崂山区香港东路123号",
                "烟台市芝罘区南大街123号",
                "威海市环翠区青岛北路123号",
                "潍坊市奎文区胜利东街123号",
                "淄博市张店区共青团西路123号",
                "临沂市兰山区解放路123号",
                "济宁市任城区洸河路123号",
                "泰安市泰山区东岳大街123号",
                "菏泽市牡丹区中华路123号",
                "德州市德城区东风中路123号",
                "聊城市东昌府区东昌西路123号",
                "滨州市滨城区黄河八路123号",
                "东营市东营区济南路123号",
                "枣庄市市中区振兴路123号",
                "日照市东港区海曲中路123号",
                "威海市文登区文昌路123号",
                "潍坊市寿光市圣城街123号",
                "济宁市曲阜市春秋路123号",
                "泰安市新泰市府前街123号",
                "临沂市郯城县人民路123号",
                "德州市禹城市行政街123号",
                "聊城市临清市青年路123号",
                "滨州市邹平县黄山一路123号",
                "东营市垦利县振兴路123号",
                "枣庄市滕州市荆河路123号",
                "日照市莒县县城青年路123号",
                "威海市荣成市成山大道123号",
                "潍坊市青州市范公亭西路123号",
                "济宁市兖州区建设东路123号",
                "泰安市肥城市龙山路123号",
                "临沂市沂水县正阳路123号",
                "德州市齐河县晏城大街123号",
                "聊城市茌平县新政西路123号",
                "滨州市惠民县孙武四路123号",
                "东营市利津县津二路123号",
                "枣庄市山亭区府前路123号",
                "日照市五莲县解放路123号",
                "威海市乳山市胜利街123号",
                "潍坊市诸城市东关大街123号",
                "济宁市金乡县金山街123号",
                "泰安市宁阳县北关路123号",
                "临沂市兰陵县卞庄街道123号",
                "德州市陵城区唐城街123号",
                "聊城市阳谷县谷山路123号",
                "滨州市阳信县河流街123号",
                "东营市广饶县乐安大街123号",
                "枣庄市台儿庄区康乐路123号",
                "日照市岚山区黄海路123号",
                "威海市环翠区统一路123号",
                "潍坊市寒亭区民主街123号",
                "济宁市嘉祥县萌山路123号",
                "泰安市东平县东山路123号",
                "临沂市平邑县浚河路123号",
                "德州市夏津县中山街123号",
                "聊城市高唐县官道街123号",
                "滨州市博兴县博城二路123号",
                "东营市河口区海宁路123号",
                "枣庄市薛城区永福路123号",
                "日照市东港区北京路123号",
                "威海市文登区龙山路123号",
                "潍坊市昌乐县新昌路123号",
                "济宁市微山县县城东风路123号",
                "泰安市岱岳区长城路123号",
                "临沂市莒南县民主路123号",
                "德州市武城县振华街123号",
                "聊城市莘县莘亭路123号",
                "滨州市沾化县富港路123号",
                "东营市仙河镇仙河街123号",
                "枣庄市峄城区坛山路123号",
                "日照市莒县县城莒州路123号",
                "威海市荣成市成山大道中段123号"
        };

        for (int i = 0; i < addresses.length; i++) {
            PersonDto person = new PersonDto();
            person.setId((long) (i + 1));
            person.setName("测试用户" + (i + 1));
            person.setAddress(addresses[i]);
            persons.add(person);
        }

        return persons;
    }
}