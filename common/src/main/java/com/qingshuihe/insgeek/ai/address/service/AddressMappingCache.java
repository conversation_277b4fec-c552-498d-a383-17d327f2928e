// AddressMappingCache.java
package com.qingshuihe.insgeek.ai.address.service;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.qingshuihe.insgeek.ai.address.dto.AddressInfo;
import org.springframework.core.io.ClassPathResource;
import org.springframework.stereotype.Component;
import org.springframework.util.StreamUtils;

import javax.annotation.PostConstruct;
import java.io.InputStream;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

@Component
public class AddressMappingCache {

    private Map<String, AddressInfo> cityMappingCache = new ConcurrentHashMap<>();
    private TrieTree trieTree;

    @PostConstruct
    public void init() {
        loadAddressMapping();
        buildTrieTree();
    }

    private void loadAddressMapping() {
        try {
            ClassPathResource resource = new ClassPathResource("json/address_mapping_zh-CN.json");
            InputStream inputStream = resource.getInputStream();
            String jsonContent = StreamUtils.copyToString(inputStream, StandardCharsets.UTF_8);

            ObjectMapper objectMapper = new ObjectMapper();
            Map<String, Map<String, Object>> rawMapping = objectMapper.readValue(jsonContent, Map.class);

            for (Map.Entry<String, Map<String, Object>> entry : rawMapping.entrySet()) {
                String cityName = entry.getKey();
                Map<String, Object> value = entry.getValue();

                AddressInfo addressInfo = new AddressInfo();
                addressInfo.setProv((String) value.get("prov"));
                addressInfo.setCity((String) value.get("city"));
                Object codeObj = value.get("code");
                if (codeObj instanceof Integer) {
                    addressInfo.setCode((Integer) codeObj);
                } else if (codeObj instanceof String) {
                    addressInfo.setCode(Integer.parseInt((String) codeObj));
                }

                cityMappingCache.put(cityName, addressInfo);
            }
        } catch (Exception e) {
            throw new RuntimeException("Failed to load address mapping data", e);
        }
    }

    private void buildTrieTree() {
        trieTree = new TrieTree();
        for (String city : cityMappingCache.keySet()) {
            trieTree.insert(city);
        }
    }

    public AddressInfo getAddressInfo(String city) {
        return cityMappingCache.get(city);
    }

    /**
     * 优化后的城市查找方法，支持更灵活的匹配
     *
     * @param address 待解析的地址字符串
     * @return 匹配到的城市名称列表
     */
    public List<String> findPossibleCities(String address) {
        // 使用优化后的查找方法
        List<String> possibleCities = trieTree.findAllCities(address);

        // 如果没有找到，尝试使用原有的方法作为备选
        if (possibleCities.isEmpty()) {
            possibleCities = trieTree.searchPrefixes(address);
        }

        return possibleCities;
    }

    /**
     * 新增方法：根据城市名称获取地址信息
     *
     * @param cities 城市名称列表
     * @return 对应的地址信息列表
     */
    public List<AddressInfo> getAddressInfos(List<String> cities) {
        List<AddressInfo> addressInfos = new ArrayList<>();
        for (String city : cities) {
            AddressInfo info = cityMappingCache.get(city);
            if (info != null) {
                addressInfos.add(info);
            }
        }
        return addressInfos;
    }
}
