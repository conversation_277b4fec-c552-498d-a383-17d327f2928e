// AddressMappingCache.java
package com.qingshuihe.insgeek.ai.address.service;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.qingshuihe.insgeek.ai.address.dto.AddressInfo;
import org.springframework.core.io.ClassPathResource;
import org.springframework.stereotype.Component;
import org.springframework.util.StreamUtils;

import javax.annotation.PostConstruct;
import java.io.InputStream;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

@Component
public class AddressMappingCache {

    private Map<String, AddressInfo> cityMappingCache = new ConcurrentHashMap<>();
    private Map<String, String> cityAliasMapping = new ConcurrentHashMap<>(); // 城市别名映射
    private TrieTree trieTree;

    @PostConstruct
    public void init() {
        loadAddressMapping();
        buildCityAliasMapping();
        buildTrieTree();
    }

    private void loadAddressMapping() {
        try {
            ClassPathResource resource = new ClassPathResource("json/address_mapping_zh-CN.json");
            InputStream inputStream = resource.getInputStream();
            String jsonContent = StreamUtils.copyToString(inputStream, StandardCharsets.UTF_8);

            ObjectMapper objectMapper = new ObjectMapper();
            Map<String, Map<String, Object>> rawMapping = objectMapper.readValue(jsonContent, Map.class);

            for (Map.Entry<String, Map<String, Object>> entry : rawMapping.entrySet()) {
                String cityName = entry.getKey();
                Map<String, Object> value = entry.getValue();

                AddressInfo addressInfo = new AddressInfo();
                addressInfo.setProv((String) value.get("prov"));
                addressInfo.setCity((String) value.get("city"));
                Object codeObj = value.get("code");
                if (codeObj instanceof Integer) {
                    addressInfo.setCode((Integer) codeObj);
                } else if (codeObj instanceof String) {
                    addressInfo.setCode(Integer.parseInt((String) codeObj));
                }

                cityMappingCache.put(cityName, addressInfo);
            }
        } catch (Exception e) {
            throw new RuntimeException("Failed to load address mapping data", e);
        }
    }

    /**
     * 构建城市别名映射
     * 为每个城市创建简化名称的映射，例如：深圳 -> 深圳市
     */
    private void buildCityAliasMapping() {
        for (String fullCityName : cityMappingCache.keySet()) {
            // 移除"市"后缀创建别名
            if (fullCityName.endsWith("市")) {
                String shortName = fullCityName.substring(0, fullCityName.length() - 1);
                cityAliasMapping.put(shortName, fullCityName);
            }

            // 处理特殊情况，如"北京市" -> "北京"
            if (fullCityName.equals("北京市")) {
                cityAliasMapping.put("北京", fullCityName);
            } else if (fullCityName.equals("上海市")) {
                cityAliasMapping.put("上海", fullCityName);
            } else if (fullCityName.equals("天津市")) {
                cityAliasMapping.put("天津", fullCityName);
            } else if (fullCityName.equals("重庆市")) {
                cityAliasMapping.put("重庆", fullCityName);
            }
        }
    }

    private void buildTrieTree() {
        trieTree = new TrieTree();

        // 添加完整城市名
        for (String city : cityMappingCache.keySet()) {
            trieTree.insert(city);
        }

        // 添加城市别名
        for (String alias : cityAliasMapping.keySet()) {
            trieTree.insert(alias);
        }
    }

    public AddressInfo getAddressInfo(String city) {
        // 首先尝试直接获取
        AddressInfo info = cityMappingCache.get(city);
        if (info != null) {
            return info;
        }

        // 如果没找到，尝试通过别名映射获取
        String fullCityName = cityAliasMapping.get(city);
        if (fullCityName != null) {
            return cityMappingCache.get(fullCityName);
        }

        return null;
    }

    /**
     * 优化后的城市查找方法，支持更灵活的匹配
     *
     * @param address 待解析的地址字符串
     * @return 匹配到的城市名称列表
     */
    public List<String> findPossibleCities(String address) {
        // 使用优化后的查找方法
        List<String> possibleCities = trieTree.findAllCities(address);

        // 如果没有找到，尝试使用原有的方法作为备选
        if (possibleCities.isEmpty()) {
            possibleCities = trieTree.searchPrefixes(address);
        }

        return possibleCities;
    }

    /**
     * 新增方法：根据城市名称获取地址信息
     *
     * @param cities 城市名称列表
     * @return 对应的地址信息列表
     */
    public List<AddressInfo> getAddressInfos(List<String> cities) {
        List<AddressInfo> addressInfos = new ArrayList<>();
        for (String city : cities) {
            AddressInfo info = cityMappingCache.get(city);
            if (info != null) {
                addressInfos.add(info);
            }
        }
        return addressInfos;
    }

    /**
     * 高性能的城市匹配方法，支持位置感知
     *
     * @param address 待解析的地址字符串
     * @return 匹配结果列表，包含位置信息
     */
    public List<TrieTree.CityMatchResult> findCityMatchesWithPosition(String address) {
        return trieTree.findCityMatchesWithPosition(address);
    }

    /**
     * 检查指定的字符串是否是有效的城市名
     *
     * @param cityName 城市名称
     * @return 如果是有效城市名返回true，否则返回false
     */
    public boolean isValidCity(String cityName) {
        return cityMappingCache.containsKey(cityName) || cityAliasMapping.containsKey(cityName);
    }
}
