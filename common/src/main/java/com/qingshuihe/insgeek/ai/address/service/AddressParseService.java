package com.qingshuihe.insgeek.ai.address.service;

import com.qingshuihe.insgeek.ai.address.dto.AddressInfo;
import com.qingshuihe.insgeek.ai.address.dto.PersonDto;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;

// AddressParseService.java
@Service
public class AddressParseService {
    
    @Autowired
    private AddressMappingCache addressMappingCache;
    
    public void parsePersonAddresses(List<PersonDto> personList) {
        // 批量处理，提高效率
        personList.parallelStream().forEach(person -> {
            // 使用优化的地址解析逻辑
            parseAddressOptimized(person, person.getAddress());
        });
    }

    /**
     * 解析地址字符串列表，返回解析结果
     * 优化版本：支持多城市识别，位置优先级选择，从长到短匹配
     */
    public List<PersonDto> parseAddressStrings(List<String> addresses) {
        List<PersonDto> results = new ArrayList<>();

        for (int i = 0; i < addresses.size(); i++) {
            String addressText = addresses.get(i);
            PersonDto person = new PersonDto();
            person.setId((long) (i + 1));
            person.setAddress(addressText);

            // 使用优化的地址解析逻辑
            parseAddressOptimized(person, addressText);

            results.add(person);
        }

        return results;
    }
    
    private void parseAddress(PersonDto person) {
        String address = person.getAddress();
        if (address == null || address.isEmpty()) {
            return;
        }
        
        // 使用前缀树快速查找可能的城市名
        List<String> possibleCities = addressMappingCache.findPossibleCities(address);
        
        // 选择最长匹配的城市名（更精确）
        String matchedCity = possibleCities.stream()
                .max(Comparator.comparingInt(String::length))
                .orElse(null);
        
        if (matchedCity != null) {
            AddressInfo addressInfo = addressMappingCache.getAddressInfo(matchedCity);
            if (addressInfo != null) {
                person.setMedicareProv(addressInfo.getProv());
                person.setMedicareCity(addressInfo.getCity());
            }
        }
    }

    /**
     * 只解析地址中的省市信息
     */
    private void parseAddressOnly(PersonDto person, String addressText) {
        if (addressText == null || addressText.isEmpty()) {
            return;
        }

        // 使用前缀树快速查找可能的城市名
        List<String> possibleCities = addressMappingCache.findPossibleCities(addressText);

        // 选择最长匹配的城市名（更精确）
        String matchedCity = possibleCities.stream()
                .max(Comparator.comparingInt(String::length))
                .orElse(null);

        if (matchedCity != null) {
            AddressInfo addressInfo = addressMappingCache.getAddressInfo(matchedCity);
            if (addressInfo != null) {
                person.setMedicareProv(addressInfo.getProv());
                person.setMedicareCity(addressInfo.getCity());

                // 设置原始文本
                person.setName(""); // 清空姓名，因为我们只关注地址
                person.setAddress(addressText); // 保留原始地址文本
            }
        }
    }

    /**
     * 优化的地址解析方法
     * 实现需求：
     * 1. 从长到短的匹配逻辑
     * 2. 多个城市时优先选择第一个出现的
     * 3. 高性能处理
     */
    private void parseAddressOptimized(PersonDto person, String addressText) {
        if (addressText == null || addressText.isEmpty()) {
            return;
        }

        // 使用高性能的前缀树匹配方法
        List<TrieTree.CityMatchResult> cityMatches = addressMappingCache.findCityMatchesWithPosition(addressText);

        if (cityMatches.isEmpty()) {
            return;
        }

        // 根据需求选择最佳匹配：
        // 1. 优先选择第一个出现的位置
        // 2. 在相同位置的情况下，选择最长的匹配
        // 3. 处理重叠情况：如果有重叠的匹配，选择最长的
        TrieTree.CityMatchResult bestMatch = selectBestMatch(cityMatches);

        if (bestMatch != null) {
            AddressInfo addressInfo = addressMappingCache.getAddressInfo(bestMatch.getCityName());
            if (addressInfo != null) {
                person.setMedicareProv(addressInfo.getProv());
                person.setMedicareCity(addressInfo.getCity());
                // 设置原始文本
                person.setAddress(addressText); // 保留原始地址文本
            }
        }
    }

    /**
     * 选择最佳匹配的城市
     * 算法逻辑：
     * 1. 找出所有重叠的匹配组
     * 2. 在每个重叠组中选择最长的匹配
     * 3. 在所有非重叠的匹配中选择第一个出现的
     */
    private TrieTree.CityMatchResult selectBestMatch(List<TrieTree.CityMatchResult> cityMatches) {
        if (cityMatches.isEmpty()) {
            return null;
        }

        // 按位置排序
        cityMatches.sort(Comparator.comparingInt(TrieTree.CityMatchResult::getStartPosition));

        // 处理重叠匹配，选择每个重叠组中最长的匹配
        List<TrieTree.CityMatchResult> finalMatches = new ArrayList<>();

        for (TrieTree.CityMatchResult current : cityMatches) {
            // 查找与当前匹配重叠的所有匹配
            List<TrieTree.CityMatchResult> overlappingGroup = new ArrayList<>();
            overlappingGroup.add(current);

            // 检查是否与已处理的匹配重叠
            boolean alreadyProcessed = false;
            for (TrieTree.CityMatchResult processed : finalMatches) {
                if (isOverlapping(current, processed)) {
                    alreadyProcessed = true;
                    break;
                }
            }

            if (alreadyProcessed) {
                continue; // 跳过已经处理过的重叠匹配
            }

            // 找出所有与current重叠的匹配
            for (TrieTree.CityMatchResult other : cityMatches) {
                if (other != current && isOverlapping(current, other)) {
                    overlappingGroup.add(other);
                }
            }

            // 在重叠组中选择最长的匹配
            TrieTree.CityMatchResult bestInGroup = overlappingGroup.stream()
                    .max(Comparator.comparingInt((TrieTree.CityMatchResult m) -> m.getCityName().length())
                            .thenComparing(Comparator.comparingInt(TrieTree.CityMatchResult::getStartPosition)))
                    .orElse(current);

            finalMatches.add(bestInGroup);
        }

        // 返回第一个（位置最靠前的）匹配
        return finalMatches.stream()
                .min(Comparator.comparingInt(TrieTree.CityMatchResult::getStartPosition))
                .orElse(null);
    }

    /**
     * 检查两个匹配是否重叠
     */
    private boolean isOverlapping(TrieTree.CityMatchResult match1, TrieTree.CityMatchResult match2) {
        // 两个区间 [start1, end1] 和 [start2, end2] 重叠的条件是：
        // start1 <= end2 && start2 <= end1
        return match1.getStartPosition() <= match2.getEndPosition() &&
               match2.getStartPosition() <= match1.getEndPosition();
    }
}
