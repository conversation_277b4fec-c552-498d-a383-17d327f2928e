package com.qingshuihe.insgeek.ai.address.service;

import com.qingshuihe.insgeek.ai.address.dto.AddressInfo;
import com.qingshuihe.insgeek.ai.address.dto.PersonDto;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;

// AddressParseService.java
@Service
public class AddressParseService {
    
    @Autowired
    private AddressMappingCache addressMappingCache;
    
    public void parsePersonAddresses(List<PersonDto> personList) {
        // 批量处理，提高效率
        personList.parallelStream().forEach(person -> {
            parseAddress(person);
        });
    }

    /**
     * 解析地址字符串列表，返回解析结果
     */
    public List<PersonDto> parseAddressStrings(List<String> addresses) {
        List<PersonDto> results = new ArrayList<>();

        for (int i = 0; i < addresses.size(); i++) {
            String addressText = addresses.get(i);
            PersonDto person = new PersonDto();
            person.setId((long) (i + 1));
            person.setAddress(addressText);

            // 解析地址中的省市信息
            parseAddressOnly(person, addressText);

            results.add(person);
        }

        return results;
    }
    
    private void parseAddress(PersonDto person) {
        String address = person.getAddress();
        if (address == null || address.isEmpty()) {
            return;
        }
        
        // 使用前缀树快速查找可能的城市名
        List<String> possibleCities = addressMappingCache.findPossibleCities(address);
        
        // 选择最长匹配的城市名（更精确）
        String matchedCity = possibleCities.stream()
                .max(Comparator.comparingInt(String::length))
                .orElse(null);
        
        if (matchedCity != null) {
            AddressInfo addressInfo = addressMappingCache.getAddressInfo(matchedCity);
            if (addressInfo != null) {
                person.setMedicareProv(addressInfo.getProv());
                person.setMedicareCity(addressInfo.getCity());
            }
        }
    }

    /**
     * 只解析地址中的省市信息
     */
    private void parseAddressOnly(PersonDto person, String addressText) {
        if (addressText == null || addressText.isEmpty()) {
            return;
        }

        // 使用前缀树快速查找可能的城市名
        List<String> possibleCities = addressMappingCache.findPossibleCities(addressText);

        // 选择最长匹配的城市名（更精确）
        String matchedCity = possibleCities.stream()
                .max(Comparator.comparingInt(String::length))
                .orElse(null);

        if (matchedCity != null) {
            AddressInfo addressInfo = addressMappingCache.getAddressInfo(matchedCity);
            if (addressInfo != null) {
                person.setMedicareProv(addressInfo.getProv());
                person.setMedicareCity(addressInfo.getCity());

                // 设置原始文本
                person.setName(""); // 清空姓名，因为我们只关注地址
                person.setAddress(addressText); // 保留原始地址文本
            }
        }
    }
}
