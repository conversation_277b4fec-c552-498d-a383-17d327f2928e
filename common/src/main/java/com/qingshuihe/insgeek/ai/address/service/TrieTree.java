// TrieTree.java
package com.qingshuihe.insgeek.ai.address.service;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class TrieTree {
    private TrieNode root;

    public TrieTree() {
        root = new TrieNode();
    }

    public void insert(String word) {
        TrieNode node = root;
        for (char c : word.toCharArray()) {
            node.children.putIfAbsent(c, new TrieNode());
            node = node.children.get(c);
        }
        node.isEnd = true;
        node.word = word;
    }

    /**
     * 优化后的前缀搜索方法，支持部分匹配和最长匹配
     *
     * @param text 待搜索的文本
     * @return 匹配到的城市名称列表
     */
    public List<String> searchPrefixes(String text) {
        List<String> results = new ArrayList<>();
        // 使用Set避免重复结果
        java.util.Set<String> uniqueResults = new java.util.HashSet<>();

        for (int i = 0; i < text.length(); i++) {
            TrieNode node = root;
            // 记录当前路径上所有匹配的城市名称
            List<String> currentMatches = new ArrayList<>();

            for (int j = i; j < text.length(); j++) {
                char c = text.charAt(j);
                if (!node.children.containsKey(c)) {
                    break;
                }
                node = node.children.get(c);
                if (node.isEnd) {
                    currentMatches.add(node.word);
                }
            }

            // 如果找到了匹配项，选择最长的匹配
            if (!currentMatches.isEmpty()) {
                // 按长度排序，选择最长的匹配
                currentMatches.sort((a, b) -> b.length() - a.length());
                uniqueResults.add(currentMatches.get(0));
            }
        }

        // 过滤掉被包含的短匹配项（例如如果匹配到"深圳市"，就不需要"深圳"）
        results.addAll(uniqueResults);
        filterContainedMatches(results);

        return results;
    }

    /**
     * 过滤掉被包含的短匹配项
     *
     * @param matches 匹配结果列表
     */
    private void filterContainedMatches(List<String> matches) {
        if (matches.size() <= 1) return;

        // 创建副本用于比较
        List<String> toRemove = new ArrayList<>();
        for (int i = 0; i < matches.size(); i++) {
            for (int j = 0; j < matches.size(); j++) {
                if (i != j && matches.get(j).contains(matches.get(i))
                        && matches.get(j).length() > matches.get(i).length()) {
                    toRemove.add(matches.get(i));
                    break;
                }
            }
        }
        matches.removeAll(toRemove);
    }

    /**
     * 新增方法：查找文本中所有可能的城市名称（包括子字符串匹配）
     *
     * @param text 待搜索的文本
     * @return 所有匹配到的城市名称
     */
    public List<String> findAllCities(String text) {
        List<String> results = new ArrayList<>();
        java.util.Set<String> foundCities = new java.util.HashSet<>();

        // 遍历文本中的每个位置
        for (int i = 0; i < text.length(); i++) {
            // 从当前字符开始，尝试匹配所有可能的城市名称
            matchCitiesFromPosition(text, i, foundCities);
        }

        results.addAll(foundCities);
        return results;
    }

    /**
     * 从指定位置开始匹配城市名称
     *
     * @param text 文本
     * @param startPos 起始位置
     * @param foundCities 已找到的城市集合
     */
    private void matchCitiesFromPosition(String text, int startPos, java.util.Set<String> foundCities) {
        TrieNode node = root;

        for (int i = startPos; i < text.length(); i++) {
            char c = text.charAt(i);
            if (!node.children.containsKey(c)) {
                break;
            }
            node = node.children.get(c);
            if (node.isEnd) {
                foundCities.add(node.word);
            }
        }
    }

    private static class TrieNode {
        Map<Character, TrieNode> children = new HashMap<>();
        boolean isEnd = false;
        String word;
    }
}
