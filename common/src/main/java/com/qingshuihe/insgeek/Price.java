package com.qingshuihe.insgeek;

/**
 * @Description:
 * @Author: shl
 * @Date: 2025/4/1
 **/
import com.fasterxml.jackson.annotation.JsonProperty;

public class Price {
    @JsonProperty("amount")
    private String amount;

    @JsonProperty("currency")
    private String currency;

    // Getters and Setters
    // ...

    public String getAmount() {
        return amount;
    }

    public void setAmount(String amount) {
        this.amount = amount;
    }

    public String getCurrency() {
        return currency;
    }

    public void setCurrency(String currency) {
        this.currency = currency;
    }

    @Override
    public String toString() {
        return "Price{" +
                "amount='" + amount + '\'' +
                ", currency='" + currency + '\'' +
                '}';
    }
}
