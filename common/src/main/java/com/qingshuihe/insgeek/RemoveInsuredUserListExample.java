package com.qingshuihe.insgeek;

/**
 * @Description:
 * @Author: shl
 * @Date: 2025/1/13
 **/
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

public class RemoveInsuredUserListExample {

    public static void main(String[] args) {
        // 创建原始的 removeInsuredUserList
        List<User> removeInsuredUserList = new ArrayList<>();
        removeInsuredUserList.add(new User(1L, "Alice"));
        removeInsuredUserList.add(new User(2L, "Bob"));
        removeInsuredUserList.add(new User(3L, "Charlie"));
        removeInsuredUserList.add(new User(1L, "Alice"));

        // 过滤 removeInsuredUserList 并创建新的 filteredList
        List<User> filteredList = removeInsuredUserList.stream()
                .filter(user -> user.getId() != 1L)
                .collect(Collectors.toList());

        // 打印原始 removeInsuredUserList 和过滤后的 filteredList
        System.out.println("Original removeInsuredUserList: " + removeInsuredUserList);
        System.out.println("Filtered filteredList: " + filteredList);

        // 继续使用原始的 removeInsuredUserList 进行其他操作
        removeInsuredUserList.removeIf(user -> user.getName().equals("Bob"));
        System.out.println("Updated removeInsuredUserList: " + removeInsuredUserList);
    }

    static class User {
        private Long id;
        private String name;

        public User(Long id, String name) {
            this.id = id;
            this.name = name;
        }

        public Long getId() {
            return id;
        }

        public String getName() {
            return name;
        }
    }
}
