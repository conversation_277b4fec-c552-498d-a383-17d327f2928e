package com.qingshuihe.study;

import java.util.*;

/**
 * @Description:
 * @Author: shl
 * @Date: 2024/6/13
 **/
public class Sum {

    /**
     * @Description: 在有序数组中删除重复两次以上的元素，并输出数组长度
     * 思路：双指针，分类讨论，重复一次或不重复，快慢指针前进并赋值，计数器状态处理；重复多次，快指针前进，慢指针不动，计数器累加
     * @Date: 2024/6/15
     * @Param array:
     **/
    public static int removeDuplicates(int[] nums) {
        int count = 0;
        if (nums.length == 0 || nums.length == 1) {
            return nums.length;
        }
        int i = 0;
        int j = 1;
        while (j < nums.length) {
            if (nums[i] == nums[j]) {
                count++;
                if (count < 2) {//diyici zhaodao chognfuzhi
                    i++;
                    nums[i] = nums[j];
                }
            } else {
                nums[i + 1] = nums[j];
                count = 0;
                i++;
            }
            j++;
        }
        for (int k = 0; k <= i; k++) {
            System.out.print(nums[k]);
        }
        System.out.println();
        return i + 1;
    }

    /**
     * @Description: 移除等于VAL的元素
     * @Date: 2024/6/15
     * @Param nums:
     * @Param val:
     **/
    public int removeElement(int[] nums, int val) {
        if (nums.length <= 1) {
            return nums.length;
        }
        int i = 0, j = 0;
        while (j < nums.length) {
            if (nums[j] != val) {
                nums[i] = nums[j];
                i++;
            }
            j++;
        }
        return i;
    }

    /**
     * @Description: 给定一个排序数组和一个目标值，在数组中找到目标值，并返回其索引。如果目标值不存在于数组中，返回它将会被按顺序插入的位置。
     * @Date: 2024/6/15
     * @Param nums:
     * @Param target: TODO
     **/
    public int searchInsert(int[] nums, int target) {
        int head = 0;
        int tail = nums.length - 1;
        int mid = 0;
        while (head <= tail) {
            mid = (head + tail) / 2;
            if (nums[mid] < target) {
                head = mid + 1;
            } else if (nums[mid] > target) {
                tail = mid - 1;
            }
        }
        return mid;
    }

    /**
     * @Description: 给你两个按 非递减顺序 排列的整数数组 nums1 和 nums2，另有两个整数 m 和 n ，分别表示 nums1 和 nums2 中的元素数目。
     * <p>
     * 请你 合并 nums2 到 nums1 中，使合并后的数组同样按 非递减顺序 排列。
     * <p>
     * 注意：最终，合并后数组不应由函数返回，而是存储在数组 nums1 中。为了应对这种情况，nums1 的初始长度为 m + n，其中前 m 个元素表示应合并的元素，后 n 个元素为 0 ，应忽略。nums2 的长度为 n 。
     * @Date: 2024/6/15
     * @Param nums1:
     * @Param m:
     * @Param nums2:
     * @Param n:解法：逆向双指针，从尾部n1放更大饿值
     **/
    public void merge(int[] nums1, int m, int[] nums2, int n) {
        int tail = m + n - 1;
        int i, k;
        for (i = m - 1, k = n - 1; i >= 0 && k >= 0; tail--) {
            if (nums2[k] >= nums1[i]) {
                nums1[tail] = nums2[k];
                k--;
            } else {
                nums1[tail] = nums1[i];
                i--;
            }
        }
        while (i >= 0) {
            nums1[tail] = nums1[i];
            i--;
            tail--;
        }
        while (k >= 0) {
            nums1[tail] = nums2[k];
            k--;
            tail--;
        }
    }

    /**
     * @Description: 给定一个字符串 s ，请你找出其中不含有重复字符的 最长
     * 子串
     * 的长度
     * @Date: 2024/6/15
     * @Param s:
     **/
    public int lengthOfLongestSubstring(String s) {
        int res = 0;
        int n = s.length();
        if (n <= 1) {
            return s.length();
        }
        int l = 0, r = 1;
        Set<Character> set = new HashSet<>();
        set.add(s.charAt(l));
        while (r < n) {
            while (r < n && !set.contains(s.charAt(r))) {
                set.add(s.charAt(r));
                r++;
            }
            res = Math.max(res, r - l);
            l++;
            set.remove(s.charAt(l));
        }
        return res;
    }

    /**
     * @Description: 一个函数来查找字符串数组中的最长公共前缀。
     * <p>
     * 如果不存在公共前缀，返回空字符串 ""。
     * @Date: 2024/6/15
     * @Param strs:
     **/
    public String longestCommonPrefix(String[] strs) {
        if (strs.length == 0) {
            return "";
        }
        String res = strs[0];
        for (int i = 1; i < strs.length; i++) {
            if (res.isEmpty()) {
                return res;
            }
            int k = 0;
            while (k < res.length() && k < strs[i].length()) {
                if (res.charAt(k) != strs[i].charAt(k)) {
                    break;
                }
                k++;
            }
            res = res.substring(0, k);
        }
        return res;
    }


    /**
     * @Description: 给你两个字符串 haystack 和 needle ，请你在 haystack 字符串中找出 needle 字符串的第一个匹配项的下标（下标从 0 开始）。如果 needle 不是 haystack 的一部分，则返回  -1 。
     * @Date: 2024/6/15
     * @Param haystack:
     * @Param needle:
     **/
    public int strStr(String haystack, String needle) {
        int i = 0, j = 0;
        if (haystack.length() < needle.length()) {
            return -1;
        }
        int head = 0;
        while (j < needle.length() && i < haystack.length()) {
            if (haystack.charAt(i) != needle.charAt(j)) {
                head++;
                j = 0;
                i = head;
            } else {
                j++;
                i++;
            }
        }
        if (j == needle.length()) {
            return head;
        }
        return -1;
    }

    /**
     * @Description: 169.多数元素
     * 给定一个大小为 n 的数组 nums ，返回其中的多数元素。多数元素是指在数组中出现次数 大于 ⌊ n/2 ⌋ 的元素。
     * @Date: 2024/6/15
     * @Param nums:hash解决，摩尔投票法
     **/
    public int majorityElement(int[] nums) {
//        HashMap<Integer, Integer> map = new HashMap<>();
//        for (int i = 0; i < nums.length; i++) {
//            map.put(nums[i],map.getOrDefault(nums[i],0)+1);
//            if (map.get(nums[i])>nums.length/2){
//                return nums[i];
//            }
//        }
//        return -1;
        int x = 0, vote = 0;
        for (int i = 0; i < nums.length; i++) {
            if (vote == 0) {
                x = nums[i];
                vote++;
            } else {
                if (x != nums[i]) {
                    vote--;
                } else {
                    vote++;
                }
            }
        }
        return x;
    }

    /**
     * @Description: 189. 轮转数组
     * 给定一个整数数组 nums，将数组中的元素向右轮转 k 个位置，其中 k 是非负数
     * 输入: nums = [1,2,3,4,5,6,7], k = 3
     * 输出: [5,6,7,1,2,3,4]
     * @Date: 2024/6/15
     * @Param nums:
     * @Param k:
     **/
    public void rotate(int[] nums, int k) {
        if (nums.length <= k) {
            k = k % nums.length;
        }
        int n = nums.length;
        int[] temp = new int[k];
        for (int i = 0, j = n - 1; i < k; i++, j--) {
            temp[i] = nums[j];
        }
        for (int i = n - k - 1, j = n - 1; i >= 0; i--, j--) {
            nums[j] = nums[i];
        }
        for (int i = 0, j = k - 1; i < k; i++, j--) {
            nums[j] = temp[i];
        }
    }

    /**
     * @Description: 121. 买卖股票的最佳时机
     * 给定一个数组 prices ，它的第 i 个元素 prices[i] 表示一支给定股票第 i 天的价格。
     * <p>
     * 你只能选择 某一天 买入这只股票，并选择在 未来的某一个不同的日子 卖出该股票。设计一个算法来计算你所能获取的最大利润。
     * <p>
     * 返回你可以从这笔交易中获取的最大利润。如果你不能获取任何利润，返回 0
     * [7,1,5,3,6,4]
     * 输出：5
     * @Date: 2024/6/15
     * @Param prices:贪心算法：最小的时候买入，遇到比自己大的就比较一次利润，留下最大的利润
     **/
    public int maxProfit(int[] prices) {
        int max = 0;
        int min = prices[0];
        for (int i = 1; i < prices.length; i++) {
            if (prices[i] < min) {
                min = prices[i];
            } else {
                max = Math.max(max, prices[i] - min);
            }
        }
        return max;
    }

    /**
     * @Description: 买卖股票的最佳时机 II
     * 给你一个整数数组 prices ，其中 prices[i] 表示某支股票第 i 天的价格。
     * <p>
     * 在每一天，你可以决定是否购买和/或出售股票。你在任何时候 最多 只能持有 一股 股票。你也可以先购买，然后在 同一天 出售。
     * <p>
     * 返回 你能获得的 最大 利润 。
     * @Date: 2024/6/15
     * @Param prices: 继续贪心算法：只要遇到比买入价格高的，就计算收益，然后立即再买
     **/
    public int maxProfit2(int[] prices) {
        int max = 0;
        int min = prices[0];
//        for (int i = 1; i < prices.length;i++) {
//            if (prices[i]<min){
//                min = prices[i];
//            }else {
//                if (i+1<prices.length&&prices[i+1]<prices[i]){
//                    max = max +prices[i]-min;
//                    min = prices[i+1];
//                }
//                if (i+1==prices.length){
//                    max = max +prices[i]-min;
//                }
//            }
//        }
        for (int i = 0; i < prices.length; i++) {
            if (prices[i] >= min) {
                max = max + prices[i] - min;
                min = prices[i];
            } else {
                min = prices[i];
            }
        }
        return max;
    }

    /**
     * @Description:55跳跃游戏 给你一个非负整数数组 nums ，你最初位于数组的 第一个下标 。数组中的每个元素代表你在该位置可以跳跃的最大长度。
     * <p>
     * 判断你是否能够到达最后一个下标，如果可以，返回 true ；否则，返回 false 。
     * @Date: 2024/6/15
     * @Param nums:
     **/
    public boolean canJump(int[] nums) {
        int max = 0;
        for (int i = 0; i < nums.length; i++) {
            if (max >= i) {
                max = Math.max(i + nums[i], max);
                if (max >= nums.length - 1) {
                    return true;
                }
            }
        }
        return false;
    }

    /**
     * @Description: 二叉树的中序遍历
     * @Date: 2024/6/26
     * @Param root:
     **/
    public List<Integer> inorderTraversal(TreeNode root) {
        ArrayList<Integer> integers = new ArrayList<>();
        dfs(integers, root);
        return integers;
    }

    public void dfs(List<Integer> list, TreeNode treeNode) {
        if (treeNode == null) {
            return;
        }
        dfs(list, treeNode.left);
        list.add(treeNode.val);
        dfs(list, treeNode.right);

    }


    /**
     * @Description: 95. 不同的二叉搜索树 II
     * @Date: 2024/6/26
     * @Param n:
     * 给你一个整数 n ，请你生成并返回所有由 n 个节点组成且节点值从 1 到 n 互不相同的不同 二叉搜索树 。可以按 任意顺序 返回答案。
     **/
//    public List<TreeNode> generateTrees(int n) {
//        if ()
//    }

    /**
     * @Description: TODO
     * @Date: 2024/6/26
     * @Param p:
     * @Param q:
     * 100. 相同的树
     * 给你两棵二叉树的根节点 p 和 q ，编写一个函数来检验这两棵树是否相同。
     * <p>
     * 如果两个树在结构上相同，并且节点具有相同的值，则认为它们是相同的。
     * 深度优先搜索
     **/
    public boolean isSameTree(TreeNode p, TreeNode q) {

        if (p == null && q == null) {
            return true;
        } else if (p == null || q == null) {
            return false;
        } else if (p.val != q.val) {
            return false;
        }
        return isSameTree(p.left, q.left) && isSameTree(p.right, q.right);
    }

    /**
     * @Description: TODO
     * @Date: 2024/6/26
     * @Param root:
     * 101. 对称二叉树
     * 给你一个二叉树的根节点 root ， 检查它是否轴对称
     **/
    public boolean isSymmetric(TreeNode root) {
        return isSymmetricImpl(root, root);
    }

    public boolean isSymmetricImpl(TreeNode left, TreeNode right) {
        if (left == null && right == null) {
            return true;
        } else if (left == null || right == null) {
            return false;
        }
        return left.val == right.val && isSymmetricImpl(left.left, right.right) && isSymmetricImpl(left.right, right.left);
    }


    /**
     * @Description: TODO
     * @Date: 2024/6/26
     * @Param root:
     * <p>
     * 104. 二叉树的最大深度
     * 给定一个二叉树 root ，返回其最大深度。
     * <p>
     * 二叉树的 最大深度 是指从根节点到最远叶子节点的最长路径上的节点数。
     **/
//    public int maxDepth(TreeNode root) {
//        return maxDepthImpl(root,0);
//    }
//    public int maxDepthImpl(TreeNode treeNode,int maxDepthNumber){
//        if (treeNode==null){
//            return maxDepthNumber;
//        }
//        maxDepthNumber++;
//        return Math.max(maxDepthImpl(treeNode.left,maxDepthNumber), maxDepthImpl(treeNode.right,maxDepthNumber));
//    }
    public int maxDepth(TreeNode root) {
        if (root == null) {
            return 0;
        }
        return Math.max(maxDepth(root.left), maxDepth(root.right)) + 1;
    }

    /**
     * @Description: TODO
     * @Date: 2024/6/26
     * @Param nums:
     * 108. 将有序数组转换为二叉搜索树
     * 给你一个整数数组 nums ，其中元素已经按 升序 排列，请你将其转换为一棵
     * 平衡
     * 二叉搜索树。
     **/

    public TreeNode sortedArrayToBST(int[] nums) {
        return sortedArrayToBSTImpl(nums, 0, nums.length - 1);
    }

    public TreeNode sortedArrayToBSTImpl(int[] nums, int left, int right) {
        if (left > right) {
            return null;
        }
        int mid = (left + right) / 2;
        TreeNode treeNode = new TreeNode(nums[mid]);
        treeNode.left = sortedArrayToBSTImpl(nums, left, mid - 1);
        treeNode.right = sortedArrayToBSTImpl(nums, mid + 1, right);
        return treeNode;
    }

    /**
     * @Description: TODO
     * @Date: 2024/6/27
     * @Param root:
     * 226. 翻转二叉树
     * 给你一棵二叉树的根节点 root ，翻转这棵二叉树，并返回其根节点。
     **/
    public TreeNode invertTree(TreeNode root) {
        if (root == null) {
            return null;
        }
        TreeNode temp = root.left;
        root.left = invertTree(root.right);
        root.right = invertTree(temp);
        return root;
    }

    /**
     * @Description: TODO
     * @Date: 2024/6/28
     * <p>
     * 105. 从前序与中序遍历序列构造二叉树
     * 给定两个整数数组 preorder 和 inorder ，其中 preorder 是二叉树的先序遍历， inorder 是同一棵树的中序遍历，请构造二叉树并返回其根节点。
     * @Param preorder:
     * @Param inorder:
     * @Param preorder_left:
     * @Param preorder_right:
     * @Param inorder_left:
     * @Param inorder_right:
     * <p>
     * 对于任意一颗树而言，前序遍历的形式总是
     * <p>
     * [ 根节点, [左子树的前序遍历结果], [右子树的前序遍历结果] ]
     * 即根节点总是前序遍历中的第一个节点。而中序遍历的形式总是
     * <p>
     * [ [左子树的中序遍历结果], 根节点, [右子树的中序遍历结果] ]
     **/
    private Map<Integer, Integer> indexMap;

    public TreeNode myBuildTree(int[] preorder, int[] inorder, int preorder_left, int preorder_right, int inorder_left, int inorder_right) {
        if (preorder_left > preorder_right) {
            return null;
        }

        // 前序遍历中的第一个节点就是根节点
        int preorder_root = preorder_left;
        // 在中序遍历中定位根节点
        int inorder_root = indexMap.get(preorder[preorder_root]);

        // 先把根节点建立出来
        TreeNode root = new TreeNode(preorder[preorder_root]);
        // 得到左子树中的节点数目
        int size_left_subtree = inorder_root - inorder_left;
        // 递归地构造左子树，并连接到根节点
        // 先序遍历中「从 左边界+1 开始的 size_left_subtree」个元素就对应了中序遍历中「从 左边界 开始到 根节点定位-1」的元素
        root.left = myBuildTree(preorder, inorder, preorder_left + 1, preorder_left + size_left_subtree, inorder_left, inorder_root - 1);
        // 递归地构造右子树，并连接到根节点
        // 先序遍历中「从 左边界+1+左子树节点数目 开始到 右边界」的元素就对应了中序遍历中「从 根节点定位+1 到 右边界」的元素
        root.right = myBuildTree(preorder, inorder, preorder_left + size_left_subtree + 1, preorder_right, inorder_root + 1, inorder_right);
        return root;
    }

    public TreeNode buildTree(int[] preorder, int[] inorder) {
        int n = preorder.length;
        // 构造哈希映射，帮助我们快速定位根节点
        HashMap<Integer, Integer> indexMap = new HashMap<Integer, Integer>();
        for (int i = 0; i < n; i++) {
            indexMap.put(inorder[i], i);
        }
        return myBuildTree(preorder, inorder, 0, n - 1, 0, n - 1);
    }

    /**
     * @Description: TODO
     * @Date: 2024/7/1
     * @Param head:
     * 82. 删除排序链表中的重复元素 II
     * 给定一个已排序的链表的头 head ， 删除原始链表中所有重复数字的节点，只留下不同的数字 。返回 已排序的链表 。
     **/

    public ListNode deleteDuplicates(ListNode head) {
        if (head == null) {
            return head;
        }
        ListNode root = new ListNode(0, head);
        ListNode cur = root;
        while (cur.next != null && cur.next.next != null) {
            if (cur.next.val == cur.next.next.val) {
                int x = cur.next.val;
                while (cur.next != null && cur.next.val == x) {
                    cur.next = cur.next.next;
                }
            } else {
                cur = cur.next;
            }
        }
        return root.next;

    }

    /**
     * @Description: TODO
     * @Date: 2024/7/2
     * @Param nums:
     * <p>
     * 45. 跳跃游戏 II
     * 给定一个长度为 n 的 0 索引整数数组 nums。初始位置为 nums[0]。
     * <p>
     * 每个元素 nums[i] 表示从索引 i 向前跳转的最大长度。换句话说，如果你在 nums[i] 处，你可以跳转到任意 nums[i + j] 处:
     * 思路一：使用贪心算法，从后往前遍历找距离尾端最远的左边端点，然后将该端点作为尾端点再次遍历找下一个最远尾端点，双循环
     **/
    public static int jump(int[] nums) {
        int count = 0;
        int max = 0;
        int r = nums.length - 1;
        int max_index = r;
        while (r > 0) {
            for (int l = r; l >= 0; l--) {
                if (nums[l] >= r - l) {
                    if (r - l >= max) {
                        max = r - l;
                        max_index = l;
                    }
                }
            }
            r = max_index;
            max = 0;
            count++;
        }
        return count;
    }

    /**
     * @Description: TODO
     * @Date: 2024/7/2
     * @Param null:
     * 274. H 指数
     * 中等
     * 给你一个整数数组 citations ，其中 citations[i] 表示研究者的第 i 篇论文被引用的次数。计算并返回该研究者的 h 指数。
     * <p>
     * 根据维基百科上 h 指数的定义：h 代表“高引用次数” ，一名科研人员的 h 指数 是指他（她）至少发表了 h 篇论文，并且 至少 有 h 篇论文被引用次数大于等于 h 。如果 h 有多种可能的值，h 指数 是其中最大的那个。
     * <p>
     * 简化为：在>=n的数组中，查找到>=n个元素，这些元素的值满足>=n
     * 思路：从1开始遍历，找到最大的满足条件的元素，最差的结果是n个n元素，也就是O（n^2）
     * 思路2：空间换时间，新建一个数组，记录发表的文章数目等于当前下标的次数，超过总长度的记为n，查询完之后，倒序找第一个大于下标的元素返回
     **/

    public int hIndex(int[] citations) {
        int n = citations.length;
        int max = 0;
        for (int i = 1; i <= n; i++) {
            int count = 0;
            for (int j = 0; j < n; j++) {
                if (citations[j] >= i) {
                    count++;
                    if (count >= i) {
                        max = i;
                        break;
                    }
                }
            }
            if (count < i) {
                break;
            }
        }
        return max;
    }

    public int hIndex_1(int[] citations) {
        int n = citations.length;
        int total = 0;
        int[] counter = new int[n + 1];
        for (int i = 0; i < n; i++) {
            if (citations[i] >= n) {
                counter[n]++;
            } else {
                counter[citations[i]]++;
            }
        }
        for (int i = n; i > 0; i--) {
            total += counter[i];//用来记录比当前值大的所有值
            if (total >= i) {
                return i;
            }
        }
        return 0;

    }


    /**
     * @Description: TODO
     * @Date: 2024/7/2
     * @Param nu
     * 238. 除自身以外数组的乘积
     * 提示
     * 给你一个整数数组 nums，返回 数组 answer ，其中 answer[i] 等于 nums 中除 nums[i] 之外其余各元素的乘积 。
     * <p>
     * 题目数据 保证 数组 nums之中任意元素的全部前缀元素和后缀的乘积都在  32 位 整数范围内。
     * <p>
     * 请 不要使用除法，且在 O(n) 时间复杂度内完成此题。
     * 思路：使用前缀数组和后缀数组，以i为分界线，l[i]=l[i-1]*num[i-1]代表i之前所有数字的乘积，r[i]=r[i+1]*r[n+1]代表i之后所有数字的乘积
     **/

    public int[] productExceptSelf(int[] nums) {
        int n = nums.length;
        if (n == 1) {
            return null;
        }
        int[] l = new int[n];
        int[] r = new int[n];
        int[] ans = new int[n];
        l[0] = 1;
        r[n - 1] = 1;
        for (int i = 1; i < n; i++) {
            l[i] = l[i - 1] * nums[i - 1];
        }
        for (int j = n - 2; j >= 0; j--) {
            r[j] = r[j + 1] * nums[j + 1];
        }
        for (int k = 0; k < n; k++) {
            ans[k] = l[k] * r[k];
        }
        return ans;
    }

    /**
     * @Description: TODO
     * @Date: 2024/7/2
     * @Param gas:
     * @Param cost:
     * 在一条环路上有 n 个加油站，其中第 i 个加油站有汽油 gas[i] 升。
     * <p>
     * 你有一辆油箱容量无限的的汽车，从第 i 个加油站开往第 i+1 个加油站需要消耗汽油 cost[i] 升。你从其中的一个加油站出发，开始时油箱为空。
     * <p>
     * 给定两个整数数组 gas 和 cost ，如果你可以按顺序绕环路行驶一周，则返回出发时加油站的编号，否则返回 -1 。如果存在解，则 保证 它是 唯一 的。
     * <p>
     * gas（余）>cost[i]
     * gas(余) = gas(余)+gas[i]-cost[i]
     * i+1>n,则i=0;
     * 当gas(总)<cost(总),肯定失败
     * 以每个i为起点进行遍历，找到是否有起点满足
     **/

    public static int canCompleteCircuit(int[] gas, int[] cost) {


        int n = gas.length;
        for (int i = 0; i < n; i++) {
            int total_gas = 0;
            int index = i;
            while (total_gas >= 0 && index < i + n) {
                total_gas = total_gas + gas[index % n] - cost[index % n];
                index++;
            }
            if (total_gas < 0) {
                continue;
            }
            if (index == i + n) {
                return i;
            }
        }
        return -1;
    }

    /**
     * @Description: TODO
     * @Date: 2024/7/2
     * @Param s:
     * 58. 最后一个单词的长度
     * 给你一个字符串 s，由若干单词组成，单词前后用一些空格字符隔开。返回字符串中 最后一个 单词的长度。
     * <p>
     * 单词 是指仅由字母组成、不包含任何空格字符的最大
     * 子字符串
     * 反向遍历，找到第一个不是空格的下标，然后开始计算单词长度，直到碰到第一个空格
     **/

    public int lengthOfLastWord(String s) {
        int n = s.length() - 1;
        while (n >= 0 && s.charAt(n) == ' ') {
            n--;
        }
        int count = 0;
        while (n >= 0 && s.charAt(n) != ' ') {
            count++;
            n--;
        }
        return count;
    }

    /**
     * @Description: TODO
     * @Date: 2024/7/2
     * @Param strs:
     * <p>
     * 14. 最长公共前缀
     * 编写一个函数来查找字符串数组中的最长公共前缀。
     * <p>
     * 如果不存在公共前缀，返回空字符串 ""。
     **/
    public String longestCommonPrefix_1(String[] strs) {
        if (strs.length == 0) {
            return "";
        }
        if (strs.length == 1) {
            return strs[0];
        }
        String res = "";
        for (int i = 0; i < strs[0].length(); i++) {
            char ch = strs[0].charAt(i);
            for (int j = 1; j < strs.length; j++) {
                if ((i > strs[j].length() - 1) || (ch != strs[j].charAt(i))) {
                    return res;
                }
            }
            res += ch;
        }
        return res;
    }


    /**
     * @Description: TODO
     * @Date: 2024/7/2
     * @Param null:
     * <p>
     * 28. 找出字符串中第一个匹配项的下标
     * 已解答
     * 简单
     * 相关标签
     * 相关企业
     * 给你两个字符串 haystack 和 needle ，请你在 haystack 字符串中找出 needle 字符串的第一个匹配项的下标（下标从 0 开始）。
     * 如果 needle 不是 haystack 的一部分，则返回  -1 。
     **/

    public int cstrStr_1(String haystack, String needle) {
        if (haystack.length() < needle.length()) {
            return -1;
        }
        for (int i = 0; i < haystack.length(); i++) {
            for (int j = 0; j < needle.length(); j++) {
                if (haystack.charAt(i + j) != needle.charAt(j)) {
                    break;
                } else if (j == needle.length() - 1) {
                    return i;
                }
            }
        }
        return -1;
    }

    /**
     * @Description: TODO
     * @Date: 2024/7/2
     * @Param null:
     * 125. 验证回文串
     * 如果在将所有大写字符转换为小写字符、并移除所有非字母数字字符之后，短语正着读和反着读都一样。则可以认为该短语是一个 回文串 。
     * <p>
     * 字母和数字都属于字母数字字符。
     * <p>
     * 给你一个字符串 s，如果它是 回文串 ，返回 true ；否则，返回 false 。
     **/

    public static boolean isPalindrome(String s) {
        int l = 0;
        int r = s.length() - 1;
        while (l <= r) {
            char lc = s.charAt(l);
            if (!Character.isLetterOrDigit(lc)) {
                l++;
                continue;
            }
            char rc = s.charAt(r);
            if (!Character.isLetterOrDigit(rc)) {
                r--;
                continue;
            }
            if (Character.toLowerCase(lc) != Character.toLowerCase(rc)) {
                return false;
            } else {
                l++;
                r--;
            }
        }
        return true;
    }

    /**
     * @Description: TODO
     * @Date: 2024/7/2
     * @Param null:
     * <p>
     * 392. 判断子序列
     * 给定字符串 s 和 t ，判断 s 是否为 t 的子序列。
     * <p>
     * 字符串的一个子序列是原始字符串删除一些（也可以不删除）字符而不改变剩余字符相对位置形成的新字符串。
     * （例如，"ace"是"abcde"的一个子序列，而"aec"不是）。
     **/
    public boolean isSubsequence(String s, String t) {
        if (s.length() > t.length()) {
            return false;
        }
        int s_index = 0;
        int t_index = 0;
        while (s_index < s.length() && t_index == t.length()) {
            if (s.charAt(s_index) == t.charAt(t_index)) {
                s_index++;
            }
            t_index++;
        }
        return s_index == s.length();
    }

    /**
     * @Description: TODO
     * @Date: 2024/7/2
     * @Param numbers:
     * @Param target:
     * <p>
     * 167. 两数之和 II - 输入有序数组
     * 给你一个下标从 1 开始的整数数组 numbers ，该数组已按 非递减顺序排列  ，请你从数组中找出满足相加之和等于目标数 target 的两个数。如果设这两个数分别是 numbers[index1] 和 numbers[index2] ，则 1 <= index1 < index2 <= numbers.length 。
     * <p>
     * 以长度为 2 的整数数组 [index1, index2] 的形式返回这两个整数的下标 index1 和 index2。
     * <p>
     * 你可以假设每个输入 只对应唯一的答案 ，而且你 不可以 重复使用相同的元素。
     * <p>
     * 你所设计的解决方案必须只使用常量级的额外空间。
     **/

    public int[] twoSum(int[] numbers, int target) {
        int l = 0;
        int r = numbers.length - 1;
        while (l < r) {
            if (numbers[l] + numbers[r] < target) {
                l++;
            } else if (numbers[l] + numbers[r] > target) {
                r--;
            } else {
                return new int[]{l + 1, r + 1};
            }
        }
        return new int[]{-1, -1};
    }

    /**
     * @Description: TODO
     * @Date: 2024/7/2
     * @Param null:
     * <p>
     * 11. 盛最多水的容器
     * 给定一个长度为 n 的整数数组 height 。有 n 条垂线，第 i 条线的两个端点是 (i, 0) 和 (i, height[i]) 。
     * <p>
     * 找出其中的两条线，使得它们与 x 轴共同构成的容器可以容纳最多的水。
     * <p>
     * 返回容器可以储存的最大水量。
     * area = (l-r)*min(num[l],num[r])
     * 双指针+贪心算法，永远移动相对较短的一边，因为较短的一边决定了水更少
     * 装多少水是由最短边决定的，为什么最短边移动，是因为如果长边移动，那么装的水可能会少，（因为由最短边决定，
     * 无论你长边移动后高度增加或者减少，都只能是装水量变少。）不可能会多。而如果移动最短边，那么有可能能够装更多的水。
     * 说明：你不能倾斜容器。
     **/
    public static int maxArea(int[] height) {
        int l = 0;
        int r = height.length - 1;
        int max = 0;
        while (l < r) {
            max = Math.max(max, (r - l) * Math.min(height[l], height[r]));
            if (height[l] < height[r]) {
                l++;
            } else {
                r--;
            }
        }
        return max;
    }

    /**
     * @Description: TODO
     * @Date: 2024/7/3
     * @Param l1:
     * @Param l2:
     * <p>
     * 2. 两数相加
     * 给你两个 非空 的链表，表示两个非负的整数。它们每位数字都是按照 逆序 的方式存储的，并且每个节点只能存储 一位 数字。
     * <p>
     * 请你将两个数相加，并以相同形式返回一个表示和的链表。
     * <p>
     * 你可以假设除了数字 0 之外，这两个数都不会以 0 开头
     **/
    public static ListNode addTwoNumbers(ListNode l1, ListNode l2) {
        int upper = 0;
        ListNode root = new ListNode();
        ListNode temp = root;
        while (l1 != null && l2 != null) {
            int sum = l1.val + l2.val + upper;
            temp.next = new ListNode(sum % 10);
            upper = sum / 10;
            temp = temp.next;
            l1 = l1.next;
            l2 = l2.next;
        }
        while (l1 != null) {
            int sum = l1.val + upper;
            temp.next = new ListNode(sum % 10);
            upper = sum / 10;
            temp = temp.next;
            l1 = l1.next;
        }
        while (l2 != null) {
            int sum = l2.val + upper;
            temp.next = new ListNode(sum % 10);
            upper = sum / 10;
            temp = temp.next;
            l2 = l2.next;
        }
        if (upper != 0) {
            temp.next = new ListNode(upper);
        }
        return root.next;
    }

    /**
     * @Description: TODO
     * @Date: 2024/7/3
     * @Param list1:
     * @Param list2:
     * <p>
     * 21. 合并两个有序链表
     * 将两个升序链表合并为一个新的 升序 链表并返回。新链表是通过拼接给定的两个链表的所有节点组成的。
     **/
    public ListNode mergeTwoLists(ListNode list1, ListNode list2) {
        if (list1 == null) {
            return list2;
        }
        if (list2 == null) {
            return list1;
        }
        if (list1.val > list2.val) {
            ListNode temp = list1;
            list1 = list2;
            list2 = temp;
        }
        ListNode root = new ListNode();
        root.next = list1;
        while (list1.next != null) {
            if (list1.next.val > list2.val) {
                ListNode temp = list1.next;
                list1.next = list2;
                list1 = list1.next;
                list2 = temp;
            } else {
                list1 = list1.next;
            }
        }
        list1.next = list2;
        return root.next;
    }

    public ListNode mergeTwoLists_1(ListNode list1, ListNode list2) {

        if (list1 == null) {
            return list2;
        } else if (list2 == null) {
            return list1;
        } else if (list1.val < list2.val) {
            list1.next = mergeTwoLists_1(list1.next, list2);
            return list1;
        } else {
            list2.next = mergeTwoLists_1(list1, list2.next);
            return list2;
        }
    }


    /**
     * @Description: TODO
     * @Date: 2024/7/3
     * @Param null:
     * 92. 反转链表 II
     * 给你单链表的头指针 head 和两个整数 left 和 right ，其中 left <= right 。
     * 请你反转从位置 left 到位置 right 的链表节点，返回 反转后的链表 。
     **/

    public static ListNode reverseBetween(ListNode head, int left, int right) {
        ListNode root = new ListNode();
        ListNode left_node_ahead = head;
        root.next = head;
        if (left > 1) {
            for (int i = 2; i < left; i++) {
                left_node_ahead = left_node_ahead.next;
            }
        } else {
            left_node_ahead = root;
        }
        //遍历结束，left_node_ahead为left的前一个节点
        Stack<ListNode> stack = new Stack<>();
        //遍历left到right，并加入stack中，同时定位到right后的一个节点，注意，这个节点可能为null
        ListNode betweenNode = left_node_ahead.next;
        for (int i = left; i < right + 1; i++) {
            stack.add(betweenNode);
            betweenNode = betweenNode.next;
        }
        //遍历完成，此时的betweenNode是right后的第一个节点
        while (!stack.isEmpty()) {
            left_node_ahead.next = stack.pop();
            left_node_ahead = left_node_ahead.next;
        }
        left_node_ahead.next = betweenNode;
        return root.next;
    }



    public static ListNode removeNthFromEnd(ListNode head, int n) {
        ListNode pre = new ListNode();
        pre.next = head;
        ListNode tail = head;
        while (n>0){
            tail =tail.next;
            n--;
        }
        while (tail!=null){
            pre = pre.next;
            tail = tail.next;
        }
        //此时pre是倒数第n个节点
        pre.next = pre.next.next;
        return head;
    }


}
