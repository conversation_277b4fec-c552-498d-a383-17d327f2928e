package com.qingshuihe.common.infrastructure.designmodel.responsibility.handler;

import com.alibaba.fastjson.JSONObject;
import com.qingshuihe.common.infrastructure.exception.AppexcepitonCodeMsg;
import com.qingshuihe.common.infrastructure.response.Resp;
import com.qingshuihe.common.infrastructure.util.http.HttpRequestUtils;
import com.qingshuihe.common.interfaces.outbond.admin.vo.RegisterUserVO;
import com.qingshuihe.common.utils.StringUtils;
import lombok.Data;

import javax.servlet.http.HttpServletRequest;

/**
 * @Description:
 * @Author: shl
 * @Date: 2023/2/7
 **/
@Data
public class NullParameterHandler extends Handler{


    private Handler nextHandler;

    @Override
    public Resp process(Object o) {
        String bodyData = HttpRequestUtils.getBodyDataByHttprequest((HttpServletRequest) o);
        if (StringUtils.isEmpty(bodyData)) {
            return Resp.error(AppexcepitonCodeMsg.NUll_PARAMETER.getCode(),AppexcepitonCodeMsg.NUll_PARAMETER.getMessage());
        }
        RegisterUserVO registerUserVO = JSONObject.toJavaObject(JSONObject.parseObject(bodyData),RegisterUserVO.class);
        if (registerUserVO.getUsername().isEmpty()){
            return Resp.error(AppexcepitonCodeMsg.NUll_PARAMETER.getCode(),AppexcepitonCodeMsg.NUll_PARAMETER.getMessage());
        }
        nextHandler = new BlackListHandler();
        return nextHandler.process(registerUserVO);
    }
}
