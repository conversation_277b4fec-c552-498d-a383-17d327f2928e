package com.qingshuihe.common.infrastructure.aspect.interfacestatus;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * @Description: 用以判断接口是否提供服务的注解
 * @Date: 2024/4/23
 * @Param null:
 **/
@Target(ElementType.METHOD)
@Retention(RetentionPolicy.RUNTIME)
public @interface  InterStatusAnnotation {
    String value() default "";
}
