package com.qingshuihe.common.infrastructure.easyexcel.utils;

/**
 * @Description:
 * @Author: shl
 * @Date: 2024/10/18
 **/

import com.alibaba.excel.ExcelReader;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import com.alibaba.excel.metadata.Sheet;
import com.alibaba.excel.support.ExcelTypeEnum;

import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.atomic.AtomicReference;

public class SplitExcelUtils {

    public static List<List<List<Object>>> splitExcel(String filePath, int chunkSize) throws IOException {
        List<List<List<Object>>> chunks = new ArrayList<>();
        AtomicInteger chunkIndex = new AtomicInteger(0);
        AtomicReference<List<List<Object>>> currentChunkRef = new AtomicReference<>(new ArrayList<>());
        File file = new File(filePath);
        if (file.exists() && file.length() > 0) {
            try (InputStream inputStream = new FileInputStream(filePath)) {
                ExcelReader reader = new ExcelReader(inputStream, ExcelTypeEnum.XLSX, null, new AnalysisEventListener<List<Object>>() {
                    int rowCount = 0; // 添加行计数器

                    @Override
                    public void invoke(List<Object> data, AnalysisContext context) {
                        System.out.println("读取到一行数据: " + data); // 添加日志
                        List<List<Object>> currentChunk = currentChunkRef.get();
                        currentChunk.add(data);
                        if (currentChunk.size() == chunkSize) {
                            chunks.add(currentChunk);
                            currentChunkRef.set(new ArrayList<>());
                        }
                        rowCount++; // 更新行计数器
                    }

                    @Override
                    public void doAfterAllAnalysed(AnalysisContext context) {
                        List<List<Object>> currentChunk = currentChunkRef.get();
                        if (!currentChunk.isEmpty()) {
                            chunks.add(currentChunk);
                        }
                        System.out.println("总共读取 " + rowCount + " 行数据"); // 添加日志
                    }

//                    @Override
//                    public void onError(Exception exception, AnalysisContext context) {
//                        System.err.println("读取 Excel 发生错误: " + exception.getMessage()); // 添加错误处理
//                        exception.printStackTrace();
//                    }
                });

                reader.read(new Sheet(0, 0)); // 从第0个sheet的第1行开始读取 (修正)
                reader.finish();
            } catch (IOException e) {
                System.err.println("读取 Excel 文件失败: " + e.getMessage());
                throw e; // 重新抛出异常，以便上层调用者处理
            }
        } else {
            System.err.println("Excel 文件不存在或大小为 0");
        }
        return chunks;
    }
}