package com.qingshuihe.common.infrastructure.designmodel.responsibility.controller;

import com.qingshuihe.common.infrastructure.designmodel.responsibility.handler.SystemErrorHandler;
import com.qingshuihe.common.infrastructure.response.Resp;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;


/**
 * @Description:
 * @Author: shl
 * @Date: 2023/2/7
 **/
@RestController
public class ResponsibilityController implements IResponsibilityController{

    @PostMapping("/responsibility/do")
    @Override
    public Resp responsibility(HttpServletRequest request) {
        SystemErrorHandler systemErrorHandler = new SystemErrorHandler();
        return systemErrorHandler.process(request);
    }
}
