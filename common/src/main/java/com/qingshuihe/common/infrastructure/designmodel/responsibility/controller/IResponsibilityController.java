package com.qingshuihe.common.infrastructure.designmodel.responsibility.controller;

import com.qingshuihe.common.infrastructure.response.Resp;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

import javax.servlet.http.HttpServletRequest;

@Api(tags = "责任链模式示例")
public interface IResponsibilityController {

    @ApiOperation(value = "/responsibility/do",notes = "责任链模式示例")
    public Resp responsibility(HttpServletRequest request);

}
