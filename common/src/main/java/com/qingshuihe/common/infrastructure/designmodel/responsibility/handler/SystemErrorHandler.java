package com.qingshuihe.common.infrastructure.designmodel.responsibility.handler;

import com.qingshuihe.common.infrastructure.exception.AppexcepitonCodeMsg;
import com.qingshuihe.common.infrastructure.response.Resp;
import lombok.Data;

import java.util.Objects;


/**
 * @Description:
 * @Author: shl
 * @Date: 2023/2/7
 **/
@Data
public class SystemErrorHandler extends Handler {

    private Handler nextHandler= new NullParameterHandler();

    @Override
    public Resp process(Object o) {
        if (Objects.isNull(o)){
            return Resp.error(AppexcepitonCodeMsg.ILLEGAl_PARAMETER.getCode(),AppexcepitonCodeMsg.ILLEGAl_PARAMETER.getMessage());
        }
        return  nextHandler.process(o);

    }
}

