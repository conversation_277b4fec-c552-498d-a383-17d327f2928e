package com.qingshuihe.common.infrastructure.es.elasticoptions.demo.entity;

import lombok.Data;
import org.springframework.data.annotation.Id;
import org.springframework.data.elasticsearch.annotations.Document;
import org.springframework.data.elasticsearch.annotations.Field;
import org.springframework.data.elasticsearch.annotations.FieldType;

import java.util.Date;

/**
 * @Description: elasticSearchOperations对应的实体类
 * @Document 表示该类为es中的一个文档
 * indexName 表示该文档的索引名
 * createIndex 表示如果没有index，则创建index
 * @Author: shl
 * @Date: 2023/2/20
 **/
@Document(indexName = "products",createIndex = true)
@Data
public class EsOperationsDemoEntity {
    //用来将放入对象id值 作为文档的_id进行映射
    @Id
    private Integer Id;
    //@Field表示映射的类型，直接写不会指定es中的字段类型，可以用type属性指定对应的文档中映射字段的类型
    @Field(type = FieldType.Keyword)
    private String name;
    @Field(type = FieldType.Double)
    private Double prices;
    @Field(type = FieldType.Text)
    private String description;
    @Field(type = FieldType.Date)
    private Date createDate;

    @Override
    public String toString() {
        return "EsOperationsDemoEntity{" +
                "Id=" + Id +
                ", name='" + name + '\'' +
                ", prices=" + prices +
                ", description='" + description + '\'' +
                ", createDate=" + createDate +
                '}';
    }
}
