package com.qingshuihe.common.infrastructure.util.http;

import javax.servlet.http.HttpServletRequest;
import java.io.*;

/**
 * @Description:
 * @Author: shl
 * @Date: 2023/2/7
 **/
public class HttpRequestUtils {
    /**
     * @Description: 1 . 字节流
     * @Date: 2023/2/7
     * @Param null:
     **/


    public static String getBodyDataByHttprequest(HttpServletRequest request) {
        InputStream inputStream = null;
        try {
            inputStream = request.getInputStream();
            BufferedInputStream byteOutputStream = new BufferedInputStream(inputStream);
            ByteArrayOutputStream byteArrayOutputStream = new ByteArrayOutputStream();
            byte[] bytes = new byte[1024];
            int a;
            while ((a = byteOutputStream.read(bytes)) != -1) {
                byteArrayOutputStream.write(bytes, 0, a);
            }
            String s = byteArrayOutputStream.toString("utf-8");
            return s;
        } catch (IOException e) {
            e.printStackTrace();
            return e.getMessage();
        } finally {
            if (inputStream != null) {
                try {
                    inputStream.close();
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }
        }
    }

    /**
     * @Description: 2 . 字符流
     * @Date: 2023/2/7
     * @Param null:
     **/

    public static String getBodyDataByReaderHttprequest(HttpServletRequest request) {

        BufferedReader reader = null;
        try {
            reader = request.getReader();
            StringBuffer sb = new StringBuffer();
            String str;
            while (null != (str = reader.readLine())) {
                sb.append(str);
            }
            return sb.toString();
        } catch (IOException e) {
            e.printStackTrace();
            return e.getMessage();
        } finally {
            if (null == reader) {
                try {
                    reader.close();
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }
        }
    }
/**
 * @Description: RequestBody方式 直接Map 或者 对象来接收 ,这个需要知道对方传输过来的样式
 * @Date: 2023/2/7
 * @Param null:
 **/


}
