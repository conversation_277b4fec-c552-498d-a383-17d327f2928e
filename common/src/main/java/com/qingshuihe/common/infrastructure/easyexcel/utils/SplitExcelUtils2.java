//package com.qingshuihe.common.infrastructure.easyexcel.utils;
//
//import com.alibaba.excel.EasyExcel;
//
//import java.io.File;
//import java.util.ArrayList;
//import java.util.List;
//import java.util.concurrent.*;
//
//public class SplitExcelUtils2 {
//
//    private static final int BATCH_SIZE = 2000; // 每个小表格的行数
//    private static final int THREAD_COUNT = 5; // 线程数量
//
//    public static void main(String[] args) throws Exception {
//        String inputFilePath = "path/to/your/input.xlsx"; // 输入文件路径
//        String outputDir = "path/to/output/"; // 输出目录
//
//        // 创建线程池
//        ExecutorService executorService = Executors.newFixedThreadPool(THREAD_COUNT);
//        List<Future<String>> futures = new ArrayList<>();
//
//        // 读取大文件
//        EasyExcel.read(inputFilePath, new PageReadListener<>(dataList -> {
//            // 将数据分批处理
//            List<List<Object>> batches = splitData(dataList, BATCH_SIZE);
//            for (List<Object> batch : batches) {
//                // 提交任务到线程池
//                futures.add(executorService.submit(() -> processBatch(batch, outputDir)));
//            }
//        })).sheet().doRead();
//
//        // 等待所有任务完成并收集结果
//        for (Future<String> future : futures) {
//            try {
//                System.out.println("Processed file: " + future.get());
//            } catch (ExecutionException e) {
//                e.printStackTrace();
//            }
//        }
//
//        executorService.shutdown();
//    }
//
//    private static List<List<Object>> splitData(List<Object> dataList, int batchSize) {
//        List<List<Object>> batches = new ArrayList<>();
//        for (int i = 0; i < dataList.size(); i += batchSize) {
//            batches.add(dataList.subList(i, Math.min(i + batchSize, dataList.size())));
//        }
//        return batches;
//    }
//
//    private static String processBatch(List<Object> batch, String outputDir) {
//        String outputFilePath = outputDir + "output_" + System.currentTimeMillis() + ".xlsx";
//        EasyExcel.write(outputFilePath, batch.get(0).getClass()).sheet("Sheet1").doWrite(batch);
//        return outputFilePath;
//    }
//}
