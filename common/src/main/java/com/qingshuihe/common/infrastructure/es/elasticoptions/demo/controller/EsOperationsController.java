package com.qingshuihe.common.infrastructure.es.elasticoptions.demo.controller;

import com.qingshuihe.common.infrastructure.es.elasticoptions.demo.entity.EsOperationsDemoEntity;
import com.qingshuihe.common.infrastructure.response.Resp;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.elasticsearch.core.ElasticsearchOperations;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

/**
 * @Description:
 * @Author: shl
 * @Date: 2023/2/20
 **/
@RestController
public class EsOperationsController {

    @Autowired
    private ElasticsearchOperations elasticsearchOperations;

    @PostMapping("elasticsearch/esOperationsSave")
    @ResponseBody
    public Resp esOperationsSave(@RequestBody EsOperationsDemoEntity esOperationsDemoEntity){

        try {
            elasticsearchOperations.save(esOperationsDemoEntity);
        }catch (Exception e){
            e.printStackTrace();
        }
        return Resp.success();
    }
}
