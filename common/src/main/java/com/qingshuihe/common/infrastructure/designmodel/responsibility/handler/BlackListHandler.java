package com.qingshuihe.common.infrastructure.designmodel.responsibility.handler;

import com.qingshuihe.common.infrastructure.exception.AppexcepitonCodeMsg;
import com.qingshuihe.common.infrastructure.response.Resp;
import com.qingshuihe.common.interfaces.outbond.admin.vo.RegisterUserVO;
import lombok.Data;

/**
 * @Description:
 * @Author: shl
 * @Date: 2023/2/7
 **/
@Data
public class BlackListHandler extends Handler{
    private final String blackList = "xiaohong,xiaohei,xiaobai,xiaolv";
    @Override
    public Resp process(Object o) {
        RegisterUserVO registerUserVO = (RegisterUserVO) o;
        if (blackList.contains(registerUserVO.getUsername())){
            return Resp.error(AppexcepitonCodeMsg.BLACK_LIST_VAILD_FAILED.getCode(),AppexcepitonCodeMsg.BLACK_LIST_VAILD_FAILED.getMessage());
        }
        return Resp.success();
    }
}
