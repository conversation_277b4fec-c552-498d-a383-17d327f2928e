//package com.qingshuihe.common.infrastructure.es.config;
//
//import org.elasticsearch.client.RestHighLevelClient;
//import org.springframework.beans.factory.annotation.Value;
//import org.springframework.context.annotation.Bean;
//import org.springframework.context.annotation.Configuration;
//import org.springframework.data.elasticsearch.client.ClientConfiguration;
//import org.springframework.data.elasticsearch.client.RestClients;
//import org.springframework.data.elasticsearch.config.AbstractElasticsearchConfiguration;
//
///**
// * @Description:
// * @Author: shl
// * @Date: 2023/2/20
// **/
//@Configuration
//public class RestClientConfig extends AbstractElasticsearchConfiguration {
//
//    @Value("spring.elasticsearch.rest.uris")
//    private String esHost;
//    @Override
//    @Bean
//    public RestHighLevelClient elasticsearchClient() {
//        final ClientConfiguration clientConfiguration = ClientConfiguration.builder()
//                .connectedTo(esHost)
//                .build();
//        return RestClients.create(clientConfiguration).rest();
//    }
//}
