package com.qingshuihe.common.infrastructure.aspect.interfacestatus;

import com.qingshuihe.common.infrastructure.exception.AppexcepitonCodeMsg;
import com.qingshuihe.common.infrastructure.exception.Appexception;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Before;
import org.aspectj.lang.annotation.Pointcut;
import org.springframework.stereotype.Component;

/**
 * @Description:用以失效接口的专用注释，在接口增加该注释，可以使接口不可达，用以快速下线接口，更好的方法是在后管平台修改接口状态
 * @Author: shl
 * @Date: 2024/4/23
 **/
@Component
@Aspect
public class InterStatusAspect {

    @Pointcut(value = "@annotation(com.qingshuihe.common.infrastructure.aspect.interfacestatus.InterStatusAnnotation)")
    public void annotationPointCut() {}

    @Before("annotationPointCut()")
    Object process() throws Throwable {
        throw new Appexception(AppexcepitonCodeMsg.INTERFACE_INVAILD);
    }
}
