//package com.qingshuihe.generator;
//
//import com.baomidou.mybatisplus.annotation.DbType;
//import com.baomidou.mybatisplus.annotation.FieldFill;
//import com.baomidou.mybatisplus.annotation.IdType;
//import com.baomidou.mybatisplus.core.toolkit.StringPool;
//import com.baomidou.mybatisplus.generator.AutoGenerator;
//import com.baomidou.mybatisplus.generator.config.*;
//import com.baomidou.mybatisplus.generator.config.po.TableInfo;
//import com.baomidou.mybatisplus.generator.config.rules.DateType;
//import com.baomidou.mybatisplus.generator.config.rules.NamingStrategy;
//
//import java.util.ArrayList;
//import java.util.List;
//
//public class GeneratorCode {
//    public static void main(String[] args) {
//        // 代码生成器
//        AutoGenerator mpg = new AutoGenerator();
//
//        // 1、全局配置
//        GlobalConfig gc = new GlobalConfig();
//         String projectPath = System.getProperty("user.dir"); // 当前项目路径,user.dir找的是当前路径，不修改
//         gc.setOutputDir(projectPath + "/src/main/java");   // 放置的项目放置
//
//        //可以将生成得代码单独放在文件夹里，检查完没问题在手动将包复制到项目中
//        gc.setOutputDir("C:\\Users\\<USER>\\IdeaProjects\\generator");
//        gc.setAuthor("song");// 生成实体类的文档注解中作者名
//        gc.setOpen(false);// 生成代码后不打开本地目录
//        gc.setFileOverride(true); // 是否覆盖原来生成的
//        gc.setIdType(IdType.ID_WORKER);// 指定生成的主键类型--雪花算法生成
//        gc.setDateType(DateType.ONLY_DATE);
//        gc.setSwagger2(true);// 实体属性Swagger2 注解
//        mpg.setGlobalConfig(gc);
//
//        // 2、数据源配置
//        DataSourceConfig dsc = new DataSourceConfig();
//        dsc.setUrl("***********************************************************************************************************************");
//        dsc.setDriverName("com.mysql.cj.jdbc.Driver");
//        dsc.setUsername("qingshuihe");
//        dsc.setPassword("qingshuihe");
//        dsc.setDbType(DbType.MYSQL);
//        gc.setServiceName("%sService"); //去掉Service接口的首字母 I
//        mpg.setDataSource(dsc);
//
//        // 3、包配置
//        PackageConfig pc = new PackageConfig();
//        pc.setModuleName(null)// 生成的controller路径映射为一个"/",覆盖默认的"//"
//                .setParent("com.midjourney")// 生成controller，service，xml并设置路径。
//                .setEntity("entity")
//                .setMapper("mapper")
//                .setService("service")
//                .setServiceImpl("service.impl")
//                .setController("controller");
//        mpg.setPackageInfo(pc);
//
//        // 4、自定义配置
//        InjectionConfig cfg = new InjectionConfig() {
//            @Override
//            public void initMap() {}
//        };
//
//        String templatePath = "/templates/mapper.xml.vm";// 带上.ftl/.vm标识模板引擎
//        List<FileOutConfig> focList = new ArrayList<>();// 自定义输出配置
//        // 自定义配置会被优先输出
//        focList.add(new FileOutConfig(templatePath) {
//            @Override
//            public String outputFile(TableInfo tableInfo) {
//                // 自定义输出文件名
//                return projectPath + "/src/main/resources/mapper/" + tableInfo.getEntityName() + "Mapper" + StringPool.DOT_XML;
//            }
//        });
//        cfg.setFileOutConfigList(focList);
//        mpg.setCfg(cfg);
//
//        // 默认xml位置取消生成，传null参
//        TemplateConfig config = new TemplateConfig();
//        config.setXml(null).setController("");//注:不想生成，直接给空字符串;
//        mpg.setTemplate(config);
//
//        // 映射数据库的表名
//        String[] strTableNames ={"animals","prompts"};
//
//        // 5、策略配置
//        StrategyConfig strategy = new StrategyConfig();
//        strategy.setCapitalMode(true);// 开启全局大写命名
//        strategy.setNaming(NamingStrategy.underline_to_camel);// 实体类名驼峰
//        strategy.setColumnNaming(NamingStrategy.underline_to_camel);// 属性名驼峰
//        strategy.setInclude(strTableNames);// 设置要映射的表名
////        strategy.setSuperEntityClass(BaseEntity.class); // 生成的entity就会实现指定的Serializable接口
//        strategy.setEntityLombokModel(true); // 使用 lombok
//        strategy.setRestControllerStyle(true);// 开启 restful风格
//        strategy.setLogicDeleteFieldName("deleted");// 逻辑删除
//
//        // 6、自动填充配置（可以将自动填充注释掉，数据库中有自动填充需求就不用注释改字段就行）
//        TableFill createTime = new TableFill("create_time", FieldFill.INSERT);
//        TableFill updateTime = new TableFill("update_time", FieldFill.INSERT_UPDATE);
//        ArrayList<TableFill> tableFills = new ArrayList<>();
//        tableFills.add(createTime);
//        tableFills.add(updateTime);
//        strategy.setTableFillList(tableFills);
//
//        // 7、乐观锁(版本号)
//        strategy.setVersionFieldName("version");
//
//        strategy.setRestControllerStyle(true);
//        strategy.setControllerMappingHyphenStyle(true);
//        mpg.setStrategy(strategy);
//
//        // 执行生成器
//        mpg.execute();
//    }
//}
//
