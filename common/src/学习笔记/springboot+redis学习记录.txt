引入依赖：
<dependency>
    <groupId>org.springframework.data</groupId>
    <artifactId>spring-data-redis</artifactId>
</dependency>
<dependency>
    <groupId>redis.clients</groupId>
    <artifactId>jedis</artifactId>
</dependency>
***********************************************************************************************************
 在系统配置文件application.yaml中配置redis相关信息，包含最大线程数等等
 #设置redis的配置信息
 redis-config:
   pool:
     password:
     host: 127.0.0.1
     port: 6379
     #设置最大连接数，默认值为8.如果赋值为-1，则表示不限制；
     maxTotal: 100
     #最大空闲连接数 默认值：8
     maxIdle: 10
     #最小空闲连接数 默认值：0
     minIdle: 10
     #当资源池连接用尽后，获取Jedis连接的最大等待时间，单位毫秒，默认值为-1，表示永不超时。如果超过等待时间，则直接抛出JedisConnectionException
     maxWaitMillis: 10000
     #连接空闲多久后释放, 当空闲时间大于该值 且 空闲连接大于最大空闲连接数 时直接释放, 默认值:-1L
     softMinEvictableIdleTimeMillis: 10000
     #向资源池借用连接时，是否做连接有效性检测，无效连接会被移除，默认值：false ，业务量很大时建议为false，因为会多一次ping的开销
     testOnBorrow: true
     #向资源池归还连接时，是否做连接有效性检测，无效连接会被移除，默认值：false，业务量很大时建议为false，因为会多一次ping的开销
     testOnReturn: true
     #自动测试池中的空闲连接是否都是可用连接(是否开启空闲资源监测，默认值：false)
     testWhileIdle: true
     #当需要对空闲资源进行监测时， testWhileIdle 参数开启后与下列几个参数(timeBetweenEvictionRunsMillis minEvictableIdleTimeMillis numTestsPerEvictionRun )组合完成监测任务。
     #空闲资源的检测周期，单位为毫秒，默认值：-1，表示不检测，建议设置一个合理的值，周期性运行监测任务
     timeBetweenEvictionRunsMillis: 30000
     #资源池中资源最小空闲时间，单位为毫秒，默认值：30分钟（1000 60L 30L）=1800000，当达到该值后空闲资源将被移除，建议根据业务自身设定
     minEvictableIdleTimeMillis: 1800000
     #做空闲资源检测时，每次的采样数，默认值：3，可根据自身应用连接数进行微调，如果设置为 -1，表示对所有连接做空闲监测
     numTestsPerEvictionRun: 3
     #当资源池用尽后，调用者是否要等待(阻塞)。默认值：true，当为true时，maxWaitMillis参数才会生效，建议使用默认值, false会报异常
     blockWhenExhausted: true
     #是否启用pool的jmx管理功能, 默认true
     jmxEnabled: true
 ***********************************************************************************************************
 具体使用：
 1.设置工具类（RedisPoolPropertiesConfig.java）读取配置文件中的redis相关配置,这里需要三个注解：
 @Component是表示当前文件为一个组件支持注入 @ConfigurationProperties表示需要从配置文件中读取信息，prefix表示需要读取的文件参数
 类属性需要与配置文件中的参数保持一致
 =====
 @Component
 @ConfigurationProperties(prefix = "redis-config.pool")
 @Data
 public class RedisPoolPropertiesConfig {

     /**
      * @Description: 密码
      * @Date: 2022/9/7
      **/
     private String password;
     /**
      * @Description: 主机ip
      * @Date: 2022/9/7
      **/
     private String host;
     /**
      * @Description: 端口
      * @Date: 2022/9/7
      **/
     private int port;
     /**
      * @Description: 设置最大连接数，默认值为8.如果赋值为-1，则表示不限制；
      * @Date: 2022/9/7
      **/
     private int maxTotal;
     /**
      * @Description: 最大空闲连接数 默认值：8
      * @Date: 2022/9/7
      **/
     private int maxIdle;
     /**
      * @Description: 最小空闲连接数 默认值：0
      * @Date: 2022/9/7
      **/
     private int minIdle;
     /**
      * @Description: 当资源池连接用尽后，获取Jedis连接的最大等待时间，单位毫秒，默认值为-1，表示永不超时。如果超过等待时间，则直接抛出JedisConnectionException
      * @Date: 2022/9/7
      **/
     private int maxWaitMillis;
     /**
      * @Description: 连接空闲多久后释放, 当空闲时间大于该值 且 空闲连接大于最大空闲连接数 时直接释放, 默认值:-1L
      * @Date: 2022/9/7
      **/
     private int softMinEvictableIdleTimeMillis;
     /**
      * @Description: 向资源池借用连接时，是否做连接有效性检测，无效连接会被移除，默认值：false ，业务量很大时建议为false，因为会多一次ping的开销
      * @Date: 2022/9/7
      **/
     private boolean testOnBorrow;
     /**
      * @Description: 向资源池归还连接时，是否做连接有效性检测，无效连接会被移除，默认值：false，业务量很大时建议为false，因为会多一次ping的开销
      * @Date: 2022/9/7
      **/
     private boolean testOnReturn;
     /**
      * @Description: 自动测试池中的空闲连接是否都是可用连接(是否开启空闲资源监测，默认值：false)
      * @Date: 2022/9/7
      **/
     private boolean testWhileIdle;
     /**
      * @Description: 当需要对空闲资源进行监测时， testWhileIdle 参数开启后与下列几个参数(timeBetweenEvictionRunsMillis minEvictableIdleTimeMillis numTestsPerEvictionRun )组合完成监测任务。
      *     空闲资源的检测周期，单位为毫秒，默认值：-1，表示不检测，建议设置一个合理的值，周期性运行监测任务
      * @Date: 2022/9/7
      **/
     private int timeBetweenEvictionRunsMillis;
     /**
      * @Description: 资源池中资源最小空闲时间，单位为毫秒，默认值：30分钟（1000 60L 30L）=1800000，当达到该值后空闲资源将被移除，建议根据业务自身设定
      * @Date: 2022/9/7
      **/
     private int minEvictableIdleTimeMillis;
     /**
      * @Description: 做空闲资源检测时，每次的采样数，默认值：3，可根据自身应用连接数进行微调，如果设置为 -1，表示对所有连接做空闲监测
      * @Date: 2022/9/7
      **/
     private int numTestsPerEvictionRun;
     /**
      * @Description: 当资源池用尽后，调用者是否要等待(阻塞)。默认值：true，当为true时，maxWaitMillis参数才会生效，建议使用默认值, false会报异常
      * @Date: 2022/9/7
      **/
     private boolean blockWhenExhausted;
     /**
      * @Description: 是否启用pool的jmx管理功能, 默认true
      * @Date: 2022/9/7
      **/
     private boolean jmxEnabled;
 }

 *****************************************************************************************************************
 2.创建redis连接池RedisPoolConfig.java，注入配置文件工具（redisPoolPropertiesConfig）读取类来初始化连接池，同时初始化redis连接池工厂（JedisConnectionFactory）bean，方便后续直接使用redis
 注意这里使用到 @Configuration注解和@Bean注解，需要在项目启动时就加载该配置并初始化bean
 @Configuration
 public class RedisPoolConfig {

     @Autowired
     private RedisPoolPropertiesConfig redisPoolPropertiesConfig;

     @Bean
     public JedisPoolConfig initPoolConfig() {
         JedisPoolConfig poolConfig = new JedisPoolConfig();
         // 设置最大连接数，默认值为8.如果赋值为-1，则表示不限制；
         poolConfig.setMaxTotal(redisPoolPropertiesConfig.getMaxTotal());
         // 最大空闲连接数
         poolConfig.setMaxIdle(redisPoolPropertiesConfig.getMaxIdle());
         // 最小空闲连接数
         poolConfig.setMinIdle(redisPoolPropertiesConfig.getMinIdle());
         // 获取Jedis连接的最大等待时间，单位毫秒，默认值为-1，表示永不超时。如果超过等待时间，则直接抛出JedisConnectionException
         poolConfig.setMaxWaitMillis(redisPoolPropertiesConfig.getMaxWaitMillis());
         // 每次释放连接的最大数目
         poolConfig.setNumTestsPerEvictionRun(redisPoolPropertiesConfig.getNumTestsPerEvictionRun());
         // 释放连接的扫描间隔（毫秒）,如果为负数,则不运行逐出线程, 默认-1
         poolConfig.setTimeBetweenEvictionRunsMillis(redisPoolPropertiesConfig.getTimeBetweenEvictionRunsMillis());
         // 连接最小空闲时间
         poolConfig.setMinEvictableIdleTimeMillis(redisPoolPropertiesConfig.getMinEvictableIdleTimeMillis());
         // 连接空闲多久后释放, 当空闲时间&gt;该值 且 空闲连接&gt;最大空闲连接数 时直接释放
         poolConfig.setSoftMinEvictableIdleTimeMillis(redisPoolPropertiesConfig.getSoftMinEvictableIdleTimeMillis());
         // 在获取Jedis连接时，自动检验连接是否可用
         poolConfig.setTestOnBorrow(redisPoolPropertiesConfig.isTestOnBorrow());
         // 在将连接放回池中前，自动检验连接是否有效
         poolConfig.setTestOnReturn(redisPoolPropertiesConfig.isTestOnReturn());
         // 自动测试池中的空闲连接是否都是可用连接
         poolConfig.setTestWhileIdle(redisPoolPropertiesConfig.isTestWhileIdle());
         // 连接耗尽时是否阻塞, false报异常,ture阻塞直到超时, 默认true
         poolConfig.setBlockWhenExhausted(redisPoolPropertiesConfig.isBlockWhenExhausted());
         // 每次逐出检查时 逐出的最大数目 如果为负数就是 : 1/abs(n), 默认3
         poolConfig.setNumTestsPerEvictionRun(redisPoolPropertiesConfig.getNumTestsPerEvictionRun());
         return poolConfig;
     }



     @Bean
     public JedisConnectionFactory redisConnectionFactory() {

         RedisStandaloneConfiguration config = new RedisStandaloneConfiguration(redisPoolPropertiesConfig.getHost(), redisPoolPropertiesConfig.getPort());
         config.setPassword(redisPoolPropertiesConfig.getPassword());
         JedisConnectionFactory  redisConnectionFactory = new JedisConnectionFactory(config);
         redisConnectionFactory.setPoolConfig(initPoolConfig());
         return redisConnectionFactory;
     }
 }
*******************************************************************************************************************
3.具体使用
在需要使用到redis的地方直接注入官方支持的redis服务（
@Autowired
private StringRedisTemplate stringRedisTemplate;
使用stringRedisTemplate.opsForValue().get("key")获取redis信息
使用stringRedisTemplate.opsForValue().set("key","value")设置redis信息
使用stringRedisTemplate.delete("key")删除redis信息
如需使用redis的其他功能，可以考虑创建自定义的redisUtils工具类，自定义使用redis的其他功能，包括定时过期等

