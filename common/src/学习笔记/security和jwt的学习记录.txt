提交用户名密码权限认证过滤器和Jwt的token解析过滤器。涉及到的关键内容为：
1.用户名密码权限存储器：UsernamePasswordAuthenticationToken(在认证管理器做认证之前需要将用户信息塞入，并传给认证管理器)
2.认证管理器：authenticationManager(在AuthorizationServerConfig配置)
3.密码加密器：PasswordEncoder(在AuthorizationServerConfig配置)
4.在security内的用户信息：基于UserDetailsService去实现impl
5.JWTUtils:token生成和解析工具，用来将认证通过的信息生成token返回前端，配合token过滤器使用，对带有token的请求可以解析其用户名并获取在redis中存储的userdetails信息，该请求不用再做权限认证处理
PS：用户密码权限过滤器为：UsernamePasswordAuthenticationFilter
token校验解析过滤器为：JWTSecurityFilter
token过滤器需要配置在权限过滤器之前

学习之后个人理解的用户请求认证的整体流程（可结合学习大纲中的认证流程图）：
用户请求——>JwtFilter过滤器解析是否带有token，带有token则解析token获取权限信息，将该权限信息加入security上下文SecurityContextHolder中，不再做权限认证；(SecurityContextHolder.getContext().setAuthentication(usernamePasswordAuthenticationToken);)
不带token，则进入UsernamePasswordAuthenticationFilter，做用户名密码认证并获取对应权限
——>UsernamePasswordAuthenticationFilter的过程：
1.将用户名密码塞入UsernamePasswordAuthenticationToken存储器
2.使用认证管理器判断传入的用户名密码是否正确：authenticationManager.authenticate(usernamePasswordAuthenticationToken)，
3.认证的过程是，securityProvider会从数据库中获取到该用户的权限角色信息UserDetails，然后认证管理器会使用PasswordEncoder对传入的密码进行不可逆加密，判断两者是否匹配
注意在认证过程中认证管理器会使用PasswordEncoder对传入的密码进行不可逆加密，并与从数据库中获取的用户密码作匹配，这里可能会遇到认证卡住不动的情况，这个可能是因为数据库中的密码没有做相同的加密导致的
4.认证通过后，通过JWTUtil工具生成token信息，同时以username为key，userDetails为value存入redis信息
5.将认证通过信息返回前端，包含token信息。