SELECT istc.*
FROM ig_special_template_checkbox istc

         LEFT JOIN ig_special_template ist ON istc.data_id = ist.id
WHERE ist.delete_flg = 0
  AND istc.delete_flg = 0
  AND ist.api_key IN (
    'cWe1G0Jg9Nd'
    );



SELECT *
FROM shoebill.ig_special_template
WHERE ig_special_template.api_key IN (
    'cWe1G0Jg9Nd'
    );


-- 	checkBox需要先删除再插入
delete
from ig_special_template_checkbox
where data_id in (1744897448310225732, 1783324400448497161, 1783324400448497161);


