
# 2.特约分类迁移
# 先判断uat和pro是否存在该分类，存在则导出update语句，不存在则导出insert语句
select *
from ig_special_category
where category_name in (
    '保全追溯期'
    );

-- 分类多选框--先删后增
# 删除语句
SELECT
    CONCAT('DELETE FROM ig_special_category_checkbox WHERE id = ', iscc.id, ';') AS delete_sql
FROM
    ig_special_category_checkbox iscc
        LEFT JOIN ig_special_category isc ON isc.id = iscc.data_id
WHERE
    isc.category_name IN (
        '保全追溯期')
  AND isc.delete_flg = 0
  AND iscc.delete_flg = 0;
# 将查询结果转为插入语句导出
SELECT
    iscc.*
FROM
    ig_special_category_checkbox iscc
        LEFT JOIN ig_special_category isc ON isc.id = iscc.data_id
WHERE
    isc.category_name IN (
        '保全追溯期')
  AND isc.delete_flg = 0
  AND iscc.delete_flg = 0;
