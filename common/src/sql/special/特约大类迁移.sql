# 1.特约大类迁移
# 先判断uat和pro是否存在该分类，存在则导出update语句，不存在则导出insert语句
select *
from ig_special_large_category
where large_category_name like '财务服务%';

-- 大类多选框--先删后增
# 删除语句
SELECT CONCAT('DELETE FROM ig_special_large_category_checkbox WHERE id = ', islcc.id, ';') AS delete_sql
FROM ig_special_large_category_checkbox islcc
         LEFT JOIN ig_special_large_category islc  on islc.id = islcc.data_id
where large_category_name like '财务服务%'
  and islc.delete_flg = 0;

# 查查询结果以插入语句形式导出
select islcc.*
from ig_special_large_category_checkbox islcc
         LEFT JOIN ig_special_large_category islc  on islc.id = islcc.data_id
where large_category_name like '财务服务%'
  and islc.delete_flg = 0;


