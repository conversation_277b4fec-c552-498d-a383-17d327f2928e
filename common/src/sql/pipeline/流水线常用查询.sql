-- 根据批次号查询流水线

select a.id,a.data_id,a.`status`,a.sync_queue_type,a.created_at,a.updated_at,a.referer from ig_pipeline a where a.number in(
    '5ca74eb0-2ef2-4d83-8ae9-6401175d71dd'
    );

select a.* from ig_pipeline a where a.id = '2392387064085376577';

select * from ig_group a where a.uid = 1342476828199263000;-- 1342476828199263000

-- 根据企业id和时间查找流水线
SELECT
    *
FROM
    ig_pipeline a
WHERE
    a.data_id = 1342474457377315523
  AND a.created_at > '2025-06-19 18:24:15.959'
  AND a.created_at < '2025-06-19 18:24:18.959';

-- 	查找流水线节点
SELECT	b.`status`,a.* FROM	ig_pipeline_node a,ig_pipeline b where a.pipeline_id = b.id and b.number in(
    '5ca74eb0-2ef2-4d83-8ae9-6401175d71dd'
    );

select *from ig_plan a where a.id = 2643874355797354238;


-- 查询节点数据

select * from ig_pipeline_node_data a where a.pipeline_node_id = 2683127522204436379;

select a.created_at,a.updated_at from ig_user a WHERE a.id_number = '110101200512161988';


update ig_group_user_list set `status` = 1 , endtime = '2025-05-31 23:59:59.999' where id in (
    2561393254088951128
    );

update ig_user_insurance set state = 1 , endtime = '2025-05-31 23:59:59.999' where user_order_id in (
    2561393254088951130
    );

select distinct a.claim_service_status from ig_insurance_rules a order by a.created_at desc;
select a.claim_service_status,a.* from ig_insurance_rules a where a.eid= 2704100208288068277;
select * from ig_claim_pause_reason a where a.plan_id = 2704102183973024265;

select * from ig_plan a where a.id = 2704100208288068277;

select a.info,a.`status`,a.group_id,a.eid,a.id from ig_group_user_list a where a.id_number = 'GHZ000019';







select *from ig_user a order by a.created_at desc;

-- 	只看gul

select
    distinct a.`status`
FROM
    ig_group_user_list a,
    ig_user_order b,
    ig_user_insurance c
WHERE
    a.id = b.group_user_list_id
  AND b.id = c.user_order_id
  AND a.id_number in( 'GHZ000115') ;


SELECT a.eid,a.uid,a.plan_config,a.`status` from ig_group_user_list a where a.id_number in ();

select * from ig_user a where a.id_number = '110101199912169211';

select a.start_time,a.end_time from ig_plan a ORDER BY a.created_at desc;

SELECT a.start_time,a.end_time from ig_plan a where a.id = 2392359696553766844;

update ig_user set sync_status = 1 , platform_user_id = 6971331779 where uid = 6254449760;


select * from


             ig_plan_config a,
             ig_plan b
where a.plan_id = b.id
  and  b.id in (

    );
-- 	查询销售人员信息及渠道
SELECT * from ig_insurance_performance;
select * from ig_staff_relation_insurance ORDER BY created_at desc;
select * from p_user a where a.name = '李梦瑶';



-- 回归专用人员状态查询

SELECT
    a.id_number,
    b.created_at,
    d.plan_name,
    a.`status` AS 'gul.status',
        b.state AS 'uo.`status`',
        c.state AS 'ui.`status`' ,
        d.id
FROM
    ig_group_user_list a,
    ig_user_order b,
    ig_user_insurance c ,
    ig_plan d
WHERE
    a.id = b.group_user_list_id
  AND b.id = c.user_order_id
  AND a.eid = d.id
  AND a.id_number IN ( 'GHH000001', 'GHH000002', 'GHH000003', 'GHH000004', 'GHH000005' )
ORDER BY b.created_at desc;

select *from ig_user a;


select * from ig_user_insurance a where a.eid = 2404247839611592661;

select *from ig_task_message a where a.batch_number = 'a4e4b40c-9938-4b26-98b8-675e1c8b0096';






