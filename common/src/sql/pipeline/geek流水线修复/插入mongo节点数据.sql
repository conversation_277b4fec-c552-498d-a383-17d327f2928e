-- 插入mongo，生成DataNode的节点数据
 db.ig_pipeline_node_data.insertMany([
   {
   "_class": "com.insgeek.business.insurance.person.preservation.increase.dto.IgPipelineNodeDataMongoDB",
   "data": "{\"insurance_referer\":\"geek\",\"pipeline_num\":null,\"order_num\":\"7094548862\",\"uid\":\"116252648\",\"relation\":\"0\",\"plan_id\":\"**********\",\"group_id\":\"495230\",\"need_recommend\":null,\"upgrade_config\":[{\"relation\":\"3\",\"target_plan_config_id\":\"**********\",\"uid\":null,\"source_plan_config_id\":null,\"real_name\":\"赵小琴\",\"cert_code\":\"62242719710312450X\",\"cert_type\":\"0\",\"sex\":\"2\",\"birth\":\"***********\",\"start_time\":\"2025-01-01T00:00:00+08:00\",\"end_time\":\"2025-12-31T23:59:59.999+08:00\",\"medicare_address\":\"743000\",\"medicare_type\":1,\"price\":{\"amount\":\"2250.00\",\"currency\":\"CNY\"},\"self_buy_plan_id\":\"7094548899\",\"self_buy_trade_id\":\"7094636261\"}],\"upgrade_duty\":{\"uid\":null,\"relation\":null,\"duty_ids\":null,\"price\":null,\"self_buy_plan_id\":null,\"self_buy_trade_id\":null},\"bonus_info\":[{\"bonus_id\":\"**********\",\"bonus_type\":8,\"relation\":\"3\",\"id_number\":\"62242719710312450X\",\"sex\":\"2\",\"user_amount\":{\"amount\":\"300.00\",\"currency\":\"CNY\"}}]}",
   "pipelineNodeId": 7020255244,
   "type": "INPUT",
   "variable": "geek_plus_increase_user_data"
   }
 ]);

