//package com.qingshuihe.design;
//
//import com.baomidou.mybatisplus.core.toolkit.StringPool;
//import com.baomidou.mybatisplus.generator.config.ConstVal;
//import com.baomidou.mybatisplus.generator.config.builder.ConfigBuilder;
//import com.baomidou.mybatisplus.generator.engine.AbstractTemplateEngine;
//import com.baomidou.mybatisplus.generator.engine.FreemarkerTemplateEngine;
//import com.baomidou.mybatisplus.generator.util.FileUtils;
//import freemarker.template.Configuration;
//import freemarker.template.Template;
//import org.jetbrains.annotations.NotNull;
//
//import java.io.File;
//import java.io.FileOutputStream;
//import java.io.OutputStreamWriter;
//import java.util.HashMap;
//import java.util.Map;
//
//public class DemoGenerator  extends AbstractTemplateEngine {
//
//    private Configuration configuration;
//    public static void main(String[] args) {
//        DemoGenerator demoGenerator = new DemoGenerator();
//       String packagePath = DemoGenerator.class.getPackage().getName();
//
//        System.out.println(packagePath);
//        String entityFile = DemoGenerator.class.getResource("/").getPath().replace("/target/classes","")+"src/main/java/"+packagePath.replace(".","/")+"/demo/IDemo.java";
//        System.out.println(entityFile);
//        Map<String, Object> objectMap = new HashMap<>();
//        objectMap.put("interfacePackage",packagePath+".demo");
//        objectMap.put("interfaceName","IDemo");
//        objectMap.put("author","qishuihe");
//        Map<String, Object> table = new HashMap<>();
//        table.put("comment","freemark 测试");
//        objectMap.put("table",table);
//
//        demoGenerator.outputFile(new File(entityFile), objectMap, "/mytemplates/demo.java.ftl");
//    }
//
//
//    protected void outputFile(@NotNull File file, @NotNull Map<String, Object> objectMap, @NotNull String templatePath) {
//
//            try {
//                // 全局判断【默认】
//                boolean exist = file.exists();
//                if (!exist) {
//                    File parentFile = file.getParentFile();
//                    FileUtils.forceMkdir(parentFile);
//                }
//                writer(objectMap, templatePath, file);
//            } catch (Exception exception) {
//                throw new RuntimeException(exception);
//            }
//    }
//
//
//    @Override
//    public void writer(@NotNull Map<String, Object> objectMap, @NotNull String templatePath, @NotNull File outputFile) throws Exception {
//        configuration = new Configuration(Configuration.DEFAULT_INCOMPATIBLE_IMPROVEMENTS);
//        configuration.setDefaultEncoding(ConstVal.UTF8);
//        configuration.setClassForTemplateLoading(FreemarkerTemplateEngine.class, StringPool.SLASH);
//        Template template = configuration.getTemplate(templatePath);
//        try (FileOutputStream fileOutputStream = new FileOutputStream(outputFile)) {
//            template.process(objectMap, new OutputStreamWriter(fileOutputStream, ConstVal.UTF8));
//        }
//    }
//
//    @NotNull
//    @Override
//    public AbstractTemplateEngine init(@NotNull ConfigBuilder configBuilder) {
//        return null;
//    }
//
//    @NotNull
//    @Override
//    public String templateFilePath(@NotNull String filePath) {
//        return null;
//    }
//}
