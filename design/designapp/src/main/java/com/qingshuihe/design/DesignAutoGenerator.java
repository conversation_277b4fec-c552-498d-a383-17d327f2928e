//package com.qingshuihe.design;
//
//import com.baomidou.mybatisplus.generator.FastAutoGenerator;
//import com.baomidou.mybatisplus.generator.config.TemplateType;
//
//public class DesignAutoGenerator {
//    static String JDBCUrl ="*********************************************************************************************************************";
//    static String JDBCUsername ="qishuihe";
//    static String JDBCPassword ="qishuihe@123456";
//    public static void main(String[] args) {
//        config(JDBCUrl,JDBCUsername,JDBCPassword);
//    }
//
//    private static void config(String url, String username, String password) {
//        FastAutoGenerator.create( url,  username,  password)
//                .globalConfig(builder -> {
//                    builder.author("qishuihe") // 设置作者
//                            .enableSwagger() // 开启 swagger 模式
//                            .fileOverride() // 覆盖已生成文件
//                            .commentDate("yyyy-MM-dd hh:ss:mm")
//                            .outputDir(System.getProperty("user.dir")+"/qishuihe/design/designapp/src/main/java/");  // 指定输出目录
//                })
//                .packageConfig(builder -> {
//                    builder.parent("com.qishuihe") // 设置父包名
//                            .moduleName("design") // 设置父包模块名
//                            .entity("domain.xxx.entity")
//                            .service("domain.xxx.service")
//                            .serviceImpl("domain.xxx.service.impl")
//                            .mapper("domain.xxx.mapper")
//                            .xml("domain.xxx.mapper")
//                            .controller("application");
//                    //此处我需要将mybaties的xml配置和mapper放在同一个目录下就需要注释如下代码
////                            .pathInfo(Collections.singletonMap(OutputFile.mapperXml, System.getProperty("user.dir")+"domain/xxx/mapper")); // 设置mapperXml生成路径
//                })
//                .templateConfig(builder -> {
//                    builder.disable(TemplateType.ENTITY) // 设置父包名
//                            .entity("/mytemplates/entity.java")
//                            .service("/mytemplates/service.java")
//                            .serviceImpl("/mytemplates/serviceImpl.java")
//                            .mapper("/mytemplates/mapper.java")
//                            .mapperXml("/mytemplates/mapper.xml")
//                            .controller("/mytemplates/controller.java");
//        })
//                .strategyConfig(builder -> {
//                    builder.addTablePrefix("sys_") // 设置过滤表前缀
//                            .addTableSuffix("_t");
//                })
////                .templateEngine(new FreemarkerTemplateEngine())
//                .templateEngine(new MyFreemarkerTemplateEngine()) // 使用Freemarker引擎模板，默认的是Velocity引擎模板
//                .execute();
//    }
//}
