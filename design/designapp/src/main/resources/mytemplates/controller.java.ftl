package ${applicationPackage};


import org.springframework.web.bind.annotation.RequestMapping;

<#if restControllerStyle>
import org.springframework.web.bind.annotation.RestController;
<#else>
import org.springframework.stereotype.Controller;
</#if>
<#if interfaceName??>
import ${interfacePackage}.${interfaceName};
</#if>

/**
 * <p>
 * ${table.comment!}
 * </p>
 *
 * <AUTHOR>
 * @since ${date}
 */
<#if restControllerStyle>
@RestController
<#else>
@Controller
</#if>
@RequestMapping("<#if package.ModuleName?? && package.ModuleName != "">/${package.ModuleName}</#if>/<#if controllerMappingHyphenStyle??>${controllerMappingHyphen}<#else>${table.entityPath}</#if>")
<#if kotlin>
class ${table.controllerName}<#if interfaceName??> : ${interfaceName}()</#if>
<#else>
<#if interfaceName??>
public class ${table.controllerName} implements ${interfaceName} {
<#else>
public class ${table.controllerName} {
</#if>

}
</#if>
