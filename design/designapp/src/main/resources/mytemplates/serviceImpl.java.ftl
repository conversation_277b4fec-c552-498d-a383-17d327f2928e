package ${package.ServiceImpl?replace('xxx', entity?lower_case)};
import ${package.Entity?replace('xxx', entity?lower_case)}.${entity};
import ${package.Mapper?replace('xxx', entity?lower_case)}.${table.mapperName};
import ${package.Service?replace('xxx', entity?lower_case)}.${table.serviceName};
import ${superServiceImplClassPackage};
import org.springframework.stereotype.Service;

/**
 * <p>
 * ${table.comment!} 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since ${date}
 */
@Service
<#if kotlin>
open class ${table.serviceImplName} : ${superServiceImplClass}<${table.mapperName}, ${entity}>(), ${table.serviceName} {

}
<#else>
public class ${table.serviceImplName} extends ${superServiceImplClass}<${table.mapperName}, ${entity}> implements ${table.serviceName} {

}
</#if>
