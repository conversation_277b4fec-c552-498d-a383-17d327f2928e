# 多环境配置说明

## 环境概述

项目支持以下四个环境：

- **dev** - 开发环境：本地开发使用
- **fat** - 功能测试环境：功能测试使用  
- **uat** - 用户验收测试环境：用户验收测试使用
- **pro** - 生产环境：线上生产环境

## 配置文件结构

```
src/main/resources/
├── application.yaml          # 主配置文件（通用配置）
├── application-dev.yaml      # 开发环境配置
├── application-fat.yaml      # 功能测试环境配置
├── application-uat.yaml      # 用户验收测试环境配置
└── application-pro.yaml      # 生产环境配置
```

## 环境切换方式

### 1. 通过启动参数切换

```bash
# 开发环境
mvn spring-boot:run -pl common -Dspring-boot.run.arguments="--spring.profiles.active=dev"

# 测试环境
mvn spring-boot:run -pl common -Dspring-boot.run.arguments="--spring.profiles.active=fat"

# UAT环境
mvn spring-boot:run -pl common -Dspring-boot.run.arguments="--spring.profiles.active=uat"

# 生产环境
mvn spring-boot:run -pl common -Dspring-boot.run.arguments="--spring.profiles.active=pro"
```

### 2. 通过脚本启动

```bash
# Windows
scripts/start-dev.bat    # 开发环境
scripts/start-fat.bat    # 测试环境
scripts/start-uat.bat    # UAT环境
scripts/start-pro.bat    # 生产环境
```

### 3. 通过环境变量

```bash
export SPRING_PROFILES_ACTIVE=fat
mvn spring-boot:run -pl common
```

### 4. 通过IDE配置

在IDE中设置VM参数：`-Dspring.profiles.active=dev`

## 各环境特点

### 开发环境 (dev)
- 本地数据库和Redis
- 详细的日志输出
- SQL语句打印
- 邮件调试模式
- 较小的连接池配置

### 功能测试环境 (fat)
- 测试服务器
- 适中的日志级别
- 关闭SQL打印
- 支持环境变量配置

### 用户验收测试环境 (uat)
- 接近生产的配置
- 启用连接泄漏检测
- 手动提交Kafka消息
- 完整的监控配置

### 生产环境 (pro)
- 高性能配置
- 最小日志输出
- 强制使用环境变量
- 安全配置
- 监控和告警

## 环境变量配置

生产环境和UAT环境建议使用环境变量配置敏感信息：

```bash
# 数据库
export DB_USERNAME=your_username
export DB_PASSWORD=your_password

# Redis
export REDIS_PASSWORD=your_redis_password

# Kafka
export KAFKA_USERNAME=your_kafka_username
export KAFKA_PASSWORD=your_kafka_password

# 邮件
export MAIL_PASSWORD=your_mail_password
```

## 配置验证

启动后可通过以下端点验证配置：

- 健康检查：`http://localhost:8081/actuator/health`
- 环境信息：`http://localhost:8081/actuator/env`
- 配置属性：`http://localhost:8081/actuator/configprops`

## 注意事项

1. **生产环境安全**：生产环境配置中的敏感信息必须通过环境变量提供
2. **数据库隔离**：不同环境使用不同的数据库，避免数据污染
3. **日志级别**：生产环境使用较高的日志级别，减少性能影响
4. **连接池配置**：根据环境负载调整连接池大小
5. **监控配置**：生产环境启用完整的监控和告警
